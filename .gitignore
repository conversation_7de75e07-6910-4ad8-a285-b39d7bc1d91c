# -----------------------------------------------------------------------------
# Python-specific ignores
# -----------------------------------------------------------------------------

# Byte-compiled / optimized / DLL files
__pycache__/
*.pyc
*.pyd
*.pyo
*.egg-info/
.Python
build/
dist/
venv/
env/
.venv/

# C extensions
*.so

# Distribution / packaging
.eggs/
*.egg
sdist/
wheel/

# Virtual environment
# Recommended to ignore your virtual environment directory
# but if you use a standard name like 'venv' or 'env', it's already covered above.
# If you use a custom name, add it here:
# my_custom_venv_name/

# PyInstaller / cx_Freeze / Nuitka
# These are tools for creating standalone executables
*.spec
build-*/
dist-*/
*.pyinstaller
*.cxfreeze
*.nuitka

# Python-specific IDEs and tools
.vscode/        # VS Code specific files (settings, launch configs)
.idea/          # PyCharm / IntelliJ IDEA specific files
*.iml           # PyCharm module files
*.ipr           # PyCharm project files
*.iws           # PyCharm workspace files
.mypy_cache/    # MyPy type checker cache
.pytest_cache/  # Pytest cache directory
.ruff_cache/    # Ruff linter cache
.coverage       # Coverage.py coverage data
htmlcov/        # Coverage.py HTML report
.bandit/        # Bandit security linter cache
.git-blame-ignore-revs # Git blame ignore revisions file

# Jupyter Notebook / IPython
.ipynb_checkpoints/
*.ipynb_checkpoints/
*.nbdata
*.nbf

# Custom user files (often temporary or local-specific)
*.log
*.tmp
*.bak
*.swp
*~
.DS_Store       # macOS specific
Thumbs.db       # Windows specific

# Database files (if applicable, e.g., for SQLite)
*.sqlite3
*.db
*.sqlite

# Environment variables (sensitive information)
.env
.flaskenv
.secrets