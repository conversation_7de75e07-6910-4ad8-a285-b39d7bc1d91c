"""
配置文件
"""

# 模型配置
MODEL_ID = "iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
DEVICE = "cuda:1"  # 或 "cpu"

# 数据集配置
AISHELL_DATA_PATH = ""  # AISHELL-1数据集路径
TEST_MANIFEST = "./data/aishell/test/wav.scp"  # 测试集音频文件列表
TEST_TRANSCRIPT = "./data/aishell/test/text"   # 测试集转录文本

# 评估配置
BATCH_SIZE = 1  # 批处理大小
MAX_SAMPLES = None  # 最大测试样本数，None表示使用全部
SAMPLE_RATE = 16000  # 采样率

# 输出配置
OUTPUT_DIR = "./results"
RESULTS_FILE = "evaluation_results.json"
DETAILED_RESULTS_FILE = "detailed_results.csv"
