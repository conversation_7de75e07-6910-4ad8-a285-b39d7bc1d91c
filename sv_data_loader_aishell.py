"""
AISHELL-1数据集加载器
用于Speaker Verification任务
"""

import os
import librosa
import soundfile as sf
import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
from tqdm import tqdm
import sv_config_aishell
import random


class AishellSVDataLoader:
    """AISHELL-1数据集Speaker Verification加载器"""

    def __init__(self, data_path: str, sample_rate: int = 16000):
        self.data_path = data_path
        self.sample_rate = sample_rate
        self.speaker_utterances = {}  # 存储每个说话人的音频文件列表

    def extract_speaker_id(self, utterance_id: str) -> str:
        """从utterance_id中提取说话人ID
        AISHELL-1的utterance_id格式: BAC009S0002W0122
        说话人ID是前面的部分，例如: BAC009S0002
        """
        # AISHELL-1的说话人ID通常是utterance_id的前11个字符
        if len(utterance_id) >= 11:
            return utterance_id[:11]
        else:
            # 如果格式不标准，尝试其他方法
            parts = utterance_id.split('W')
            if len(parts) >= 2:
                return parts[0]
            else:
                return utterance_id[:7]  # 备用方案

    def load_transcript_file(self, transcript_path: str) -> Dict[str, str]:
        """加载AISHELL-1转录文件"""
        transcripts = {}
        full_path = os.path.join(self.data_path, transcript_path)

        if not os.path.exists(full_path):
            raise FileNotFoundError(f"Transcript file not found: {full_path}")

        with open(full_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split(None, 1)
                    if len(parts) >= 2:
                        utt_id = parts[0]
                        text = parts[1]
                        transcripts[utt_id] = text

        print(f"Loaded {len(transcripts)} transcripts from {transcript_path}")
        return transcripts

    def scan_audio_files(self, wav_dir: str) -> Dict[str, str]:
        """扫描音频文件目录，返回utterance_id到文件路径的映射"""
        audio_files = {}
        full_wav_dir = os.path.join(self.data_path, wav_dir)

        if not os.path.exists(full_wav_dir):
            raise FileNotFoundError(f"Audio directory not found: {full_wav_dir}")

        # 递归扫描所有音频文件
        for root, dirs, files in os.walk(full_wav_dir):
            for file in files:
                if file.endswith('.wav'):
                    # 从文件名提取utterance_id（去掉.wav扩展名）
                    utt_id = os.path.splitext(file)[0]
                    file_path = os.path.join(root, file)
                    # 转换为相对于数据集根目录的路径
                    rel_path = os.path.relpath(file_path, self.data_path)
                    audio_files[utt_id] = rel_path

        print(f"Found {len(audio_files)} audio files in {wav_dir}")
        return audio_files

    def group_by_speaker(self, utterance_dict: Dict[str, str]) -> Dict[str, List[str]]:
        """按说话人分组utterances"""
        speaker_groups = {}

        for utt_id in utterance_dict.keys():
            speaker_id = self.extract_speaker_id(utt_id)
            if speaker_id not in speaker_groups:
                speaker_groups[speaker_id] = []
            speaker_groups[speaker_id].append(utt_id)

        # 过滤掉音频数量太少的说话人
        min_utterances = sv_config_aishell.MIN_UTTERANCES_PER_SPEAKER
        filtered_groups = {
            spk: utts for spk, utts in speaker_groups.items()
            if len(utts) >= min_utterances
        }

        print(f"Found {len(speaker_groups)} speakers total")
        print(f"After filtering (min {min_utterances} utterances): {len(filtered_groups)} speakers")

        return filtered_groups

    def load_audio(self, audio_path: str) -> Tuple[Optional[np.ndarray], Optional[int]]:
        """加载音频文件"""
        try:
            # 如果是相对路径，则相对于数据集根目录
            if not os.path.isabs(audio_path):
                audio_path = os.path.join(self.data_path, audio_path)

            if not os.path.exists(audio_path):
                print(f"Warning: Audio file not found: {audio_path}")
                return None, None

            audio, sr = sf.read(audio_path)

            # 确保音频是浮点数类型
            if audio.dtype != 'float32':
                audio = audio.astype('float32')

            # 如果是立体声，转换为单声道
            if len(audio.shape) > 1:
                audio = np.mean(audio, axis=1)

            # 重采样到目标采样率
            if sr != self.sample_rate:
                audio = librosa.resample(y=audio, orig_sr=sr, target_sr=self.sample_rate)

            # 检查音频时长
            duration = len(audio) / self.sample_rate
            if duration < sv_config_aishell.MIN_DURATION:
                print(f"Warning: Audio too short ({duration:.2f}s): {audio_path}")
                return None, None

            if duration > sv_config_aishell.MAX_DURATION:
                # 截取前MAX_DURATION秒
                max_samples = int(sv_config_aishell.MAX_DURATION * self.sample_rate)
                audio = audio[:max_samples]

            # 音频标准化
            if sv_config_aishell.NORMALIZE_AUDIO:
                audio = audio / (np.max(np.abs(audio)) + 1e-8)

            return audio, self.sample_rate

        except Exception as e:
            print(f"Error loading audio {audio_path}: {e}")
            return None, None

    def create_trials(self, speaker_groups: Dict[str, List[str]],
                     audio_files: Dict[str, str]) -> List[Dict]:
        """创建speaker verification trials"""
        trials = []
        speakers = list(speaker_groups.keys())

        # 快速验证模式
        if sv_config_aishell.QUICK_VALIDATION_MODE:
            print("🚀 Quick validation mode enabled!")
            speakers = speakers[:sv_config_aishell.QUICK_MAX_SPEAKERS]
            max_same = sv_config_aishell.QUICK_MAX_SAME_SPEAKER_TRIALS
            max_diff = sv_config_aishell.QUICK_MAX_DIFF_SPEAKER_TRIALS
            print(f"Processing {len(speakers)} speakers (limited for quick validation)")
        else:
            max_same = sv_config_aishell.MAX_SAME_SPEAKER_TRIALS
            max_diff = sv_config_aishell.MAX_DIFF_SPEAKER_TRIALS

        print("Creating speaker verification trials...")

        for speaker_id in tqdm(speakers, desc="Processing speakers"):
            utterances = speaker_groups[speaker_id]

            # 随机分割为注册和测试集
            random.shuffle(utterances)
            split_idx = int(len(utterances) * sv_config_aishell.TRAIN_TEST_SPLIT_RATIO)
            enroll_utts = utterances[:split_idx] if split_idx > 0 else utterances[:1]
            test_utts = utterances[split_idx:] if split_idx < len(utterances) else utterances[1:]

            if len(test_utts) == 0:
                test_utts = utterances[-1:]  # 至少保留一个测试样本

            # 创建同说话人trials
            same_speaker_count = 0

            for enroll_utt in enroll_utts:
                for test_utt in test_utts:
                    if same_speaker_count >= max_same:
                        break
                    if enroll_utt != test_utt:  # 确保不是同一个utterance
                        trials.append({
                            'enroll_id': enroll_utt,
                            'test_id': test_utt,
                            'label': 1,  # 同说话人
                            'speaker_id': speaker_id
                        })
                        same_speaker_count += 1
                if same_speaker_count >= max_same:
                    break

            # 创建不同说话人trials
            diff_speaker_count = 0
            other_speakers = [s for s in speakers if s != speaker_id]

            for enroll_utt in enroll_utts:
                if diff_speaker_count >= max_diff:
                    break

                # 随机选择其他说话人的测试utterances
                random.shuffle(other_speakers)
                for other_speaker in other_speakers:
                    if diff_speaker_count >= max_diff:
                        break

                    other_utterances = speaker_groups[other_speaker]
                    test_utt = random.choice(other_utterances)

                    trials.append({
                        'enroll_id': enroll_utt,
                        'test_id': test_utt,
                        'label': 0,  # 不同说话人
                        'speaker_id': speaker_id,
                        'other_speaker_id': other_speaker
                    })
                    diff_speaker_count += 1

        print(f"Created {len(trials)} trials")
        same_trials = sum(1 for t in trials if t['label'] == 1)
        diff_trials = sum(1 for t in trials if t['label'] == 0)
        print(f"Same speaker trials: {same_trials}")
        print(f"Different speaker trials: {diff_trials}")

        return trials

    def get_evaluation_data(self) -> Dict:
        """获取评估数据"""
        print("Loading AISHELL-1 dataset for speaker verification...")

        # 加载转录文件
        transcripts = self.load_transcript_file(sv_config_aishell.AISHELL_TRANSCRIPT_PATH)

        # 扫描音频文件
        audio_files = self.scan_audio_files(sv_config_aishell.AISHELL_WAV_PATH)

        # 找到有音频文件的utterances
        valid_utterances = {}
        for utt_id in transcripts.keys():
            if utt_id in audio_files:
                valid_utterances[utt_id] = audio_files[utt_id]

        print(f"Found {len(valid_utterances)} valid utterances with both audio and transcript")

        # 按说话人分组
        speaker_groups = self.group_by_speaker(valid_utterances)

        # 创建trials
        trials = self.create_trials(speaker_groups, valid_utterances)

        # 创建注册和测试音频字典
        enroll_audios = {}
        test_audios = {}

        for trial in trials:
            enroll_id = trial['enroll_id']
            test_id = trial['test_id']

            if enroll_id in valid_utterances:
                enroll_audios[enroll_id] = valid_utterances[enroll_id]
            if test_id in valid_utterances:
                test_audios[test_id] = valid_utterances[test_id]

        print(f"Enrollment audios: {len(enroll_audios)}")
        print(f"Test audios: {len(test_audios)}")

        return {
            'trials': trials,
            'enroll_audios': enroll_audios,
            'test_audios': test_audios,
            'speaker_groups': speaker_groups,
            'dataset_info': {
                'total_utterances': len(transcripts),
                'valid_utterances': len(valid_utterances),
                'total_speakers': len(speaker_groups),
                'total_trials': len(trials),
                'enroll_count': len(enroll_audios),
                'test_count': len(test_audios)
            }
        }

    def save_evaluation_files(self, eval_data: Dict, output_dir: str):
        """保存评估文件到指定目录"""
        os.makedirs(output_dir, exist_ok=True)

        trials_path = os.path.join(output_dir, "trials.lst")
        enroll_path = os.path.join(output_dir, "enroll.lst")
        test_path = os.path.join(output_dir, "test.lst")

        # 保存trials文件
        with open(trials_path, 'w', encoding='utf-8') as f:
            for trial in eval_data['trials']:
                f.write(f"{trial['enroll_id']} {trial['test_id']} {trial['label']}\n")

        # 保存enroll文件
        with open(enroll_path, 'w', encoding='utf-8') as f:
            for audio_id, audio_path in eval_data['enroll_audios'].items():
                f.write(f"{audio_id} {audio_path}\n")

        # 保存test文件
        with open(test_path, 'w', encoding='utf-8') as f:
            for audio_id, audio_path in eval_data['test_audios'].items():
                f.write(f"{audio_id} {audio_path}\n")

        print(f"Saved evaluation files to {output_dir}")
        print(f"  - trials.lst: {len(eval_data['trials'])} trials")
        print(f"  - enroll.lst: {len(eval_data['enroll_audios'])} enrollment audios")
        print(f"  - test.lst: {len(eval_data['test_audios'])} test audios")
