"""
测试使用 FunASR 的 AutoModel 方法进行 speaker verification
"""

import os
import numpy as np
from funasr import AutoModel
import sv_config

def test_funasr_automodel():
    """测试 FunASR AutoModel 方法"""
    print("Testing FunASR AutoModel for speaker verification...")
    
    # 查找测试音频文件
    test_audio_path = None
    cnceleb_path = os.path.join(sv_config.CNCELEB1_DATA_PATH, "CN-Celeb_flac", "data")
    
    if os.path.exists(cnceleb_path):
        for root, dirs, files in os.walk(cnceleb_path):
            for file in files:
                if file.endswith('.flac'):
                    test_audio_path = os.path.join(root, file)
                    test_audio_path = os.path.normpath(test_audio_path)
                    break
            if test_audio_path:
                break
    
    if not test_audio_path:
        print("No audio files found for testing")
        return False
    
    print(f"Testing with audio file: {test_audio_path}")
    
    try:
        # 使用 FunASR 的 AutoModel 方法
        print("Loading model using FunASR AutoModel...")
        model = AutoModel(model="cam++")
        print("Model loaded successfully!")
        
        # 进行推理
        print("Running inference...")
        result = model.generate(input=test_audio_path)
        
        print("✓ FunASR AutoModel - Success!")
        print(f"Result type: {type(result)}")
        print(f"Result: {result}")
        
        # 如果结果是列表，查看第一个元素
        if isinstance(result, list) and len(result) > 0:
            print(f"First result: {result[0]}")
            if isinstance(result[0], dict):
                print(f"Keys in first result: {list(result[0].keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ FunASR AutoModel - Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_specific_model():
    """测试使用具体的模型ID"""
    print("\nTesting with specific model ID...")
    
    # 查找测试音频文件
    test_audio_path = None
    cnceleb_path = os.path.join(sv_config.CNCELEB1_DATA_PATH, "CN-Celeb_flac", "data")
    
    if os.path.exists(cnceleb_path):
        for root, dirs, files in os.walk(cnceleb_path):
            for file in files:
                if file.endswith('.flac'):
                    test_audio_path = os.path.join(root, file)
                    test_audio_path = os.path.normpath(test_audio_path)
                    break
            if test_audio_path:
                break
    
    if not test_audio_path:
        print("No audio files found for testing")
        return False
    
    print(f"Testing with audio file: {test_audio_path}")
    
    try:
        # 使用具体的模型ID
        print("Loading model using specific model ID...")
        model = AutoModel(model=sv_config.SV_MODEL_ID)
        print("Model loaded successfully!")
        
        # 进行推理
        print("Running inference...")
        result = model.generate(input=test_audio_path)
        
        print("✓ Specific model ID - Success!")
        print(f"Result type: {type(result)}")
        print(f"Result: {result}")
        
        # 如果结果是列表，查看第一个元素
        if isinstance(result, list) and len(result) > 0:
            print(f"First result: {result[0]}")
            if isinstance(result[0], dict):
                print(f"Keys in first result: {list(result[0].keys())}")
        
        return True
        
    except Exception as e:
        print(f"✗ Specific model ID - Failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("FunASR AutoModel Speaker Verification Test")
    print("=" * 60)
    
    try:
        # 测试 cam++ 模型
        success1 = test_funasr_automodel()
        
        # 测试具体的模型ID
        success2 = test_with_specific_model()
        
        if success1 or success2:
            print("\n✅ At least one method succeeded!")
        else:
            print("\n❌ All methods failed!")
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
