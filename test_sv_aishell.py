"""
AISHELL-1 Speaker Verification测试脚本
测试环境配置和基本功能
"""

import os
import numpy as np
import json
from datetime import datetime


def test_imports():
    """测试基本导入"""
    print("Testing imports...")

    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch: {e}")
        return False

    try:
        from funasr import AutoModel
        print("✓ FunASR")
    except ImportError as e:
        print(f"✗ FunASR: {e}")
        return False

    try:
        import sv_config_aishell
        from sv_data_loader_aishell import AishellSVDataLoader
        from sv_metrics import SVMetrics
        print("✓ AISHELL SV modules")
    except ImportError as e:
        print(f"✗ AISHELL SV modules: {e}")
        return False

    return True


def test_config():
    """测试配置"""
    print("Testing configuration...")

    try:
        import sv_config_aishell
        
        print(f"  Model ID: {sv_config_aishell.SV_MODEL_ID}")
        print(f"  Device: {sv_config_aishell.DEVICE}")
        print(f"  Dataset Path: {sv_config_aishell.AISHELL_DATA_PATH}")
        print(f"  Sample Rate: {sv_config_aishell.SAMPLE_RATE}")
        print(f"  Output Dir: {sv_config_aishell.SV_OUTPUT_DIR}")
        
        return True
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False


def test_data_loader():
    """测试数据加载器"""
    print("Testing data loader...")

    try:
        import sv_config_aishell
        from sv_data_loader_aishell import AishellSVDataLoader

        # 创建数据加载器
        data_loader = AishellSVDataLoader(
            data_path=sv_config_aishell.AISHELL_DATA_PATH,
            sample_rate=sv_config_aishell.SAMPLE_RATE
        )

        # 测试说话人ID提取
        test_utterances = [
            "BAC009S0002W0122",
            "BAC009S0003W0456", 
            "BAC009S0004W0789"
        ]
        
        for utt_id in test_utterances:
            speaker_id = data_loader.extract_speaker_id(utt_id)
            print(f"  {utt_id} -> {speaker_id}")

        print("✓ Data loader basic functions work")
        return True

    except Exception as e:
        print(f"✗ Data loader error: {e}")
        return False


def test_dataset_structure():
    """测试数据集结构"""
    print("Testing dataset structure...")

    try:
        import sv_config_aishell

        data_path = sv_config_aishell.AISHELL_DATA_PATH
        
        if not os.path.exists(data_path):
            print(f"✗ Dataset path not found: {data_path}")
            return False

        # 检查必要的文件和目录
        wav_path = os.path.join(data_path, sv_config_aishell.AISHELL_WAV_PATH)
        transcript_path = os.path.join(data_path, sv_config_aishell.AISHELL_TRANSCRIPT_PATH)

        if os.path.exists(wav_path):
            print(f"✓ Audio directory found: {wav_path}")
        else:
            print(f"✗ Audio directory not found: {wav_path}")
            return False

        if os.path.exists(transcript_path):
            print(f"✓ Transcript file found: {transcript_path}")
        else:
            print(f"✗ Transcript file not found: {transcript_path}")
            return False

        # 检查音频文件数量
        audio_count = 0
        for root, dirs, files in os.walk(wav_path):
            audio_count += len([f for f in files if f.endswith('.wav')])

        print(f"  Found {audio_count} audio files")

        if audio_count == 0:
            print("✗ No audio files found")
            return False

        return True

    except Exception as e:
        print(f"✗ Dataset structure error: {e}")
        return False


def test_metrics():
    """测试评估指标"""
    print("Testing metrics...")

    try:
        from sv_metrics import SVMetrics

        # 创建模拟数据
        np.random.seed(42)
        scores = np.random.rand(100).tolist()
        labels = ([1] * 50) + ([0] * 50)  # 50个正样本，50个负样本

        # 计算指标
        metrics = SVMetrics()
        result = metrics.calculate_all_metrics(scores, labels)

        print(f"  EER: {result['eer']:.4f}")
        print(f"  MinDCF: {result['min_dcf']:.6f}")

        return True

    except Exception as e:
        print(f"✗ Metrics error: {e}")
        return False


def run_demo():
    """运行演示"""
    print("Running AISHELL-1 speaker verification demo...")

    import sv_config_aishell
    from sv_metrics import SVMetrics

    # 创建输出目录
    os.makedirs(sv_config_aishell.SV_OUTPUT_DIR, exist_ok=True)

    # 模拟说话人验证结果
    np.random.seed(42)

    # 创建模拟数据：50个同说话人试验，50个不同说话人试验
    scores = []
    labels = []

    # 同说话人试验（高相似度）
    target_scores = np.random.normal(0.7, 0.15, 50)
    scores.extend(target_scores.tolist())
    labels.extend([1] * 50)

    # 不同说话人试验（低相似度）
    nontarget_scores = np.random.normal(0.3, 0.15, 50)
    scores.extend(nontarget_scores.tolist())
    labels.extend([0] * 50)

    # 计算评估指标
    metrics = SVMetrics()
    evaluation_metrics = metrics.calculate_all_metrics(scores, labels)

    # 显示结果
    metrics.print_metrics_summary(evaluation_metrics)

    # 保存演示结果
    demo_results = {
        'demo_info': {
            'dataset': 'AISHELL-1 (simulated)',
            'model': sv_config_aishell.SV_MODEL_ID,
            'timestamp': datetime.now().isoformat(),
            'note': 'This is a demo with simulated data'
        },
        'metrics': evaluation_metrics
    }

    demo_file = os.path.join(sv_config_aishell.SV_OUTPUT_DIR, "demo_results_aishell.json")
    with open(demo_file, 'w', encoding='utf-8') as f:
        json.dump(demo_results, f, indent=2, ensure_ascii=False)

    print(f"\n📁 Demo results saved to: {demo_file}")
    return True


def main():
    """主函数"""
    print("AISHELL-1 Speaker Verification Test")
    print("="*50)

    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Data Loader", test_data_loader),
        ("Dataset Structure", test_dataset_structure),
        ("Metrics", test_metrics),
        ("Demo", run_demo),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED: {e}")

    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} passed")

    if passed == total:
        print("🎉 All tests passed!")
        print("\nNext steps:")
        print("1. Ensure AISHELL-1 dataset is downloaded")
        print("2. Run: python prepare_aishell_sv.py")
        print("3. Run: python sv_evaluation_aishell.py")
    else:
        print("⚠️  Some tests failed. Check error messages above.")
        
        if passed >= 3:  # 如果基本功能测试通过
            print("\nBasic functionality seems OK. Dataset issues might be:")
            print("- AISHELL-1 dataset not downloaded")
            print("- Incorrect dataset path in sv_config_aishell.py")
            print("- Dataset structure not as expected")

    print("="*50)


if __name__ == "__main__":
    main()
