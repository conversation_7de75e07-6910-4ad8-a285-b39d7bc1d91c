"""
AISHELL-1数据集准备脚本
"""
import os
import tarfile
# import urllib.request # 不再需要
import requests # 引入requests
from tqdm import tqdm
import config


class DownloadProgressBar(tqdm):
    """下载进度条"""
    def update_to(self, b=1, bsize=1, tsize=None):
        if tsize is not None:
            self.total = tsize
        self.update(b * bsize - self.n)

# 修改后的 download_file 函数
def download_file(url: str, output_path: str):
    """下载文件并显示进度，支持断点续传"""
    print(f"Downloading {url}...")

    # 检查本地文件大小，用于续传
    file_size = 0
    if os.path.exists(output_path):
        file_size = os.path.getsize(output_path)
        print(f"Resuming download from {file_size} bytes.")

    headers = {'Range': f'bytes={file_size}-'} if file_size > 0 else {}

    try:
        # stream=True 允许分块下载，适合大文件
        response = requests.get(url, headers=headers, stream=True, timeout=300) # 增加超时时间
        response.raise_for_status() # 检查HTTP请求是否成功

        # 获取总文件大小
        # 如果是续传，Content-Length 可能是剩余文件的大小，需要加上已下载的部分
        total_size = int(response.headers.get('Content-Length', 0))
        if file_size > 0 and 'Content-Range' in response.headers:
            # 如果服务器支持Range请求并返回Content-Range，解析出总大小
            # Content-Range: bytes 20000000-40000000/100000000
            range_header = response.headers['Content-Range']
            total_size = int(range_header.split('/')[1])
        elif total_size == 0 and file_size > 0:
            # 如果服务器不支持Range请求或没有返回Content-Length，且已下载部分不为0
            # 这表示服务器可能返回了整个文件，而不是续传，或者服务器没有提供文件大小
            print("Warning: Server does not support resuming or did not provide Content-Length.")
            # 此时最好删除部分下载的文件并重新开始
            os.remove(output_path)
            print(f"Removed incomplete file {output_path}. Restarting download.")
            return download_file(url, output_path) # 递归调用以重新下载

        # 使用 'ab' 模式追加写入，如果是新文件则创建
        mode = 'ab' if file_size > 0 else 'wb'

        with open(output_path, mode) as f:
            with DownloadProgressBar(unit='B', unit_scale=True, miniters=1,
                                     desc=os.path.basename(output_path),
                                     total=total_size, initial=file_size) as t:
                for chunk in response.iter_content(chunk_size=8192): # 8KB chunks
                    if chunk: # filter out keep-alive new chunks
                        f.write(chunk)
                        t.update(len(chunk))
        print(f"Downloaded {output_path}")

    except requests.exceptions.RequestException as e:
        print(f"Failed to download {output_path}: {e}")
        raise # 重新抛出异常，让上层函数捕获并处理
    except Exception as e:
        print(f"An unexpected error occurred during download: {e}")
        raise


def extract_tar_gz(tar_path: str, extract_to: str):
    """解压tar.gz文件"""
    print(f"Extracting {tar_path}...")
    
    with tarfile.open(tar_path, 'r:gz') as tar:
        tar.extractall(path=extract_to)


def prepare_aishell_dataset():
    """准备AISHELL-1数据集"""
    
    # AISHELL-1数据集下载链接
    AISHELL_URLS = {
        'data': 'https://www.openslr.org/resources/33/data_aishell.tgz',
        'resource': 'https://www.openslr.org/resources/33/resource_aishell.tgz'
    }
    
    # 创建数据目录
    data_dir = config.AISHELL_DATA_PATH
    download_dir = os.path.join(data_dir, 'downloads')
    os.makedirs(download_dir, exist_ok=True)
    
    print("Preparing AISHELL-1 dataset...")
    print(f"Data directory: {data_dir}")
    
    # 下载并解压主压缩包
    for name, url in AISHELL_URLS.items():
        filename = f"{name}_aishell.tgz"
        file_path = os.path.join(download_dir, filename)
        
        try:
            # 确保主压缩包存在并完整
            if not os.path.exists(file_path) or os.path.getsize(file_path) == 0:
                download_file(url, file_path)
            else:
                # 尝试续传，如果文件不完整
                # 这一步已经由 download_file 内部处理，这里可以简化
                print(f"File {filename} already exists, checking integrity...")
                # 简单检查文件大小是否合理，或者直接让 download_file 负责
                # 如果 download_file 内部已经很健壮，这里可以跳过
                pass # 让 download_file 决定是否需要重新下载或续传

            extract_tar_gz(file_path, data_dir)
            print(f"Extracted {filename}")
        except Exception as e:
            print(f"Failed to process {filename}: {e}")
            print(f"Please manually check {file_path} and its contents.")
            continue # 跳过当前文件的后续处理，但继续下一个主文件

    # === 新增部分：解压 wav 目录下的子压缩包 ===
    print("\nExtracting individual speaker archives...")
    wav_archive_dir = os.path.join(data_dir, 'data_aishell', 'wav')
    
    if os.path.exists(wav_archive_dir) and os.path.isdir(wav_archive_dir):
        # 遍历 wav 目录下的所有 .tar.gz 文件
        for root, _, files in os.walk(wav_archive_dir):
            for file in files:
                if file.endswith('.tar.gz'):
                    archive_path = os.path.join(root, file)
                    # 解压到 wav 目录本身，这样 S0002.tar.gz 里面的 wav 就会在 wav/S0002/ 目录下
                    try:
                        extract_tar_gz(archive_path, root) 
                        # 可以在解压后删除原始的 .tar.gz 文件以节省空间
                        # os.remove(archive_path) 
                        # print(f"Removed {file}")
                    except Exception as e:
                        print(f"Failed to extract {file}: {e}")
    else:
        print(f"Warning: {wav_archive_dir} not found. Skipping speaker archive extraction.")

    # 检查数据集结构
    check_dataset_structure(data_dir)


def check_dataset_structure(data_dir: str):
    """检查数据集结构"""
    print("\nChecking dataset structure...")
    
    # 预期的文件路径
    expected_files = [
        'data_aishell/wav/test',
        'data_aishell/transcript/aishell_transcript_v0.8.txt',
        'resource_aishell/lexicon.txt',
        'resource_aishell/speaker.info'
    ]
    
    missing_files = []
    for file_path in expected_files:
        full_path = os.path.join(data_dir, file_path)
        if os.path.exists(full_path):
            print(f"✓ Found: {file_path}")
        else:
            print(f"✗ Missing: {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️  Missing {len(missing_files)} expected files/directories.")
        print("Please check the download and extraction process.")
        return False
    
    # 创建Kaldi格式的文件
    create_kaldi_format_files(data_dir)
    return True


def create_kaldi_format_files(data_dir: str):
    """创建Kaldi格式的wav.scp和text文件"""
    print("\nCreating Kaldi format files...")
    
    # 读取转录文件
    transcript_file = os.path.join(data_dir, 'data_aishell/transcript/aishell_transcript_v0.8.txt')
    if not os.path.exists(transcript_file):
        print(f"Transcript file not found: {transcript_file}")
        return
    
    # 解析转录文件
    transcripts = {}
    with open(transcript_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split(None, 1)
                if len(parts) >= 2:
                    utt_id = parts[0]
                    text = parts[1]
                    transcripts[utt_id] = text
    
    print(f"Loaded {len(transcripts)} transcripts")
    
    # 创建测试集目录
    test_dir = os.path.join(data_dir, 'test')
    os.makedirs(test_dir, exist_ok=True)
    
    # 查找测试集音频文件
    test_wav_dir = os.path.join(data_dir, 'data_aishell/wav/test')
    if not os.path.exists(test_wav_dir):
        print(f"Test wav directory not found: {test_wav_dir}")
        return
    
    # 创建wav.scp和text文件
    wav_scp_path = os.path.join(test_dir, 'wav.scp')
    text_path = os.path.join(test_dir, 'text')
    
    test_transcripts = {}
    wav_files = []
    
    # 遍历测试集音频文件
    for speaker_dir in os.listdir(test_wav_dir):
        speaker_path = os.path.join(test_wav_dir, speaker_dir)
        if os.path.isdir(speaker_path):
            for wav_file in os.listdir(speaker_path):
                if wav_file.endswith('.wav'):
                    utt_id = wav_file.replace('.wav', '')
                    wav_path = os.path.join(speaker_path, wav_file)
                    
                    if utt_id in transcripts:
                        wav_files.append((utt_id, wav_path))
                        test_transcripts[utt_id] = transcripts[utt_id]
    
    # 写入wav.scp文件
    with open(wav_scp_path, 'w', encoding='utf-8') as f:
        for utt_id, wav_path in sorted(wav_files):
            f.write(f"{utt_id} {wav_path}\n")
    
    # 写入text文件
    with open(text_path, 'w', encoding='utf-8') as f:
        for utt_id in sorted(test_transcripts.keys()):
            f.write(f"{utt_id} {test_transcripts[utt_id]}\n")
    
    print(f"Created wav.scp with {len(wav_files)} entries")
    print(f"Created text with {len(test_transcripts)} entries")
    print(f"Files saved to: {test_dir}")
    
    # 更新配置文件中的路径
    update_config_paths(data_dir)


def update_config_paths(data_dir: str):
    """更新配置文件中的路径"""
    print(f"\nDataset preparation completed!")
    print(f"Please update config.py with the following path:")
    print(f"AISHELL_DATA_PATH = '{data_dir}'")
    
    # 检查配置文件
    config_path = 'config.py'
    if os.path.exists(config_path):
        print(f"\nCurrent config.py AISHELL_DATA_PATH: {config.AISHELL_DATA_PATH}")
        if config.AISHELL_DATA_PATH != data_dir:
            print("⚠️  Path mismatch! Please update config.py manually.")


def main():
    """主函数"""
    print("AISHELL-1 Dataset Preparation Tool")
    print("=" * 50)
    
    # 检查是否已经存在数据集
    if os.path.exists(config.AISHELL_DATA_PATH):
        test_wav_scp = os.path.join(config.AISHELL_DATA_PATH, config.TEST_MANIFEST)
        test_text = os.path.join(config.AISHELL_DATA_PATH, config.TEST_TRANSCRIPT)
        
        if os.path.exists(test_wav_scp) and os.path.exists(test_text):
            print("Dataset already prepared!")
            print(f"wav.scp: {test_wav_scp}")
            print(f"text: {test_text}")
            
            # 检查文件内容
            with open(test_wav_scp, 'r') as f:
                wav_count = len(f.readlines())
            with open(test_text, 'r') as f:
                text_count = len(f.readlines())
            
            print(f"Found {wav_count} audio files and {text_count} transcripts")
            return
    
    # 准备数据集
    try:
        prepare_aishell_dataset()
        print("\n✅ Dataset preparation completed successfully!")
        print("You can now run: python asr_evaluation.py")
    except Exception as e:
        print(f"\n❌ Dataset preparation failed: {e}")
        print("Please check the error messages and try again.")


if __name__ == "__main__":
    main()
