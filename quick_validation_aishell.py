"""
AISHELL-1 Speaker Verification快速验证脚本
用于快速验证代码是否正确工作，使用较少的数据量
"""

import os
import sys
import time
from datetime import datetime

# 临时修改配置为快速验证模式
def enable_quick_mode():
    """启用快速验证模式"""
    config_file = "sv_config_aishell.py"
    
    if not os.path.exists(config_file):
        print(f"Configuration file not found: {config_file}")
        return False
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修改快速验证模式设置
    if "QUICK_VALIDATION_MODE = False" in content:
        new_content = content.replace("QUICK_VALIDATION_MODE = False", "QUICK_VALIDATION_MODE = True")
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Enabled quick validation mode")
        return True
    elif "QUICK_VALIDATION_MODE = True" in content:
        print("✅ Quick validation mode already enabled")
        return True
    else:
        print("❌ Could not find QUICK_VALIDATION_MODE setting")
        return False


def disable_quick_mode():
    """禁用快速验证模式"""
    config_file = "sv_config_aishell.py"
    
    if not os.path.exists(config_file):
        return
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 恢复正常模式
    if "QUICK_VALIDATION_MODE = True" in content:
        new_content = content.replace("QUICK_VALIDATION_MODE = True", "QUICK_VALIDATION_MODE = False")
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print("✅ Disabled quick validation mode (restored to normal)")


def run_quick_validation():
    """运行快速验证"""
    print("AISHELL-1 Speaker Verification Quick Validation")
    print("="*60)
    
    start_time = time.time()
    
    try:
        # 启用快速验证模式
        if not enable_quick_mode():
            return False
        
        # 重新导入配置以获取更新的设置
        if 'sv_config_aishell' in sys.modules:
            del sys.modules['sv_config_aishell']
        
        import sv_config_aishell
        print(f"Quick mode settings:")
        print(f"  Max speakers: {sv_config_aishell.QUICK_MAX_SPEAKERS}")
        print(f"  Max same speaker trials: {sv_config_aishell.QUICK_MAX_SAME_SPEAKER_TRIALS}")
        print(f"  Max diff speaker trials: {sv_config_aishell.QUICK_MAX_DIFF_SPEAKER_TRIALS}")
        
        # 检查数据集
        if not os.path.exists(sv_config_aishell.AISHELL_DATA_PATH):
            print(f"❌ Dataset path not found: {sv_config_aishell.AISHELL_DATA_PATH}")
            return False
        
        # 导入评估模块
        from sv_evaluation_aishell import AishellSVEvaluator
        
        print("\n🚀 Starting quick validation...")
        
        # 创建评估器
        evaluator = AishellSVEvaluator()
        
        # 加载评估数据
        print("Loading evaluation data...")
        eval_data = evaluator.data_loader.get_evaluation_data()
        
        print(f"📊 Quick validation data:")
        info = eval_data['dataset_info']
        print(f"  Total speakers: {info['total_speakers']}")
        print(f"  Total trials: {info['total_trials']}")
        print(f"  Enrollment audios: {info['enroll_count']}")
        print(f"  Test audios: {info['test_count']}")
        
        # 运行评估
        print("\n🔄 Running evaluation...")
        results = evaluator.evaluate_trials(eval_data)
        
        # 保存结果
        evaluator.save_results(results)
        
        # 显示结果
        print("\n" + "="*60)
        print("QUICK VALIDATION RESULTS")
        print("="*60)
        
        metrics = results['metrics']
        info = results['evaluation_info']
        
        print(f"Model: {info['model_id']}")
        print(f"Dataset: {info['dataset']} (Quick Validation)")
        print(f"Valid Trials: {info['valid_trials']}")
        
        print(f"\n📊 Performance Metrics:")
        print(f"  EER (Equal Error Rate): {metrics['eer']:.4f} ({metrics['eer']*100:.2f}%)")
        
        if 'mindcf' in metrics:
            print(f"  MinDCF: {metrics['mindcf']:.6f}")
        
        if 'accuracy_at_eer' in metrics:
            print(f"  Accuracy at EER: {metrics['accuracy_at_eer']:.4f} ({metrics['accuracy_at_eer']*100:.2f}%)")
        
        # 显示分数统计
        if 'score_statistics' in metrics:
            stats = metrics['score_statistics']
            print(f"\n📈 Score Statistics:")
            if 'target_scores' in stats:
                target_stats = stats['target_scores']
                print(f"  Same speaker scores: mean={target_stats['mean']:.4f}, count={target_stats['count']}")
            if 'nontarget_scores' in stats:
                nontarget_stats = stats['nontarget_scores']
                print(f"  Diff speaker scores: mean={nontarget_stats['mean']:.4f}, count={nontarget_stats['count']}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n⏱️  Quick validation completed in {duration:.1f} seconds")
        print(f"📁 Results saved to: {sv_config_aishell.SV_OUTPUT_DIR}")
        
        # 验证结果合理性
        print(f"\n🔍 Result Analysis:")
        if metrics['eer'] < 0.5:
            print("✅ EER looks reasonable (< 50%)")
        else:
            print("⚠️  EER seems high (>= 50%), check data or model")
        
        if 'score_statistics' in metrics:
            target_mean = stats['target_scores']['mean']
            nontarget_mean = stats['nontarget_scores']['mean']
            if target_mean > nontarget_mean:
                print("✅ Same speaker scores > Different speaker scores (expected)")
            else:
                print("⚠️  Same speaker scores <= Different speaker scores (unexpected)")
        
        print("\n✅ Quick validation completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Quick validation failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复正常模式
        disable_quick_mode()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AISHELL-1 Speaker Verification Quick Validation")
    parser.add_argument("--keep-quick-mode", action="store_true", 
                       help="Keep quick validation mode enabled after completion")
    
    args = parser.parse_args()
    
    success = run_quick_validation()
    
    if not args.keep_quick_mode:
        disable_quick_mode()
    else:
        print("\n📝 Note: Quick validation mode is still enabled")
        print("   Run with --keep-quick-mode=False to disable it")
    
    if success:
        print("\n🎉 Quick validation passed! The code appears to be working correctly.")
        print("\nTo run full evaluation:")
        print("1. Ensure quick mode is disabled (QUICK_VALIDATION_MODE = False)")
        print("2. Run: python sv_evaluation_aishell.py")
    else:
        print("\n💥 Quick validation failed. Please check the errors above.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
