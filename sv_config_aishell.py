"""
Speaker Verification配置文件
用于iic/speech_campplus_sv_zh-cn_16k-common模型在AISHELL-1数据集上的验证
"""

# 模型配置
SV_MODEL_ID = "iic/speech_campplus_sv_zh-cn_16k-common"
DEVICE = "cuda:1"  # 或 "cpu"

# AISHELL-1数据集配置
AISHELL_DATA_PATH = "C:/git/asr-test/data/aishell"  # AISHELL-1数据集根路径
AISHELL_WAV_PATH = "data_aishell/wav"  # 音频文件路径
AISHELL_TRANSCRIPT_PATH = "data_aishell/transcript/aishell_transcript_v0.8.txt"  # 转录文件路径
AISHELL_RESOURCE_PATH = "resource_aishell"  # 资源文件路径

# Speaker Verification评估文件路径（将自动生成）
SV_EVAL_DIR = "sv_eval"  # SV评估文件目录
EVAL_TRIALS_PATH = "sv_eval/trials.lst"  # 评估试验列表文件
EVAL_ENROLL_PATH = "sv_eval/enroll.lst"  # 注册音频列表文件
EVAL_TEST_PATH = "sv_eval/test.lst"  # 测试音频列表文件

# 音频配置
SAMPLE_RATE = 16000  # 采样率
MAX_DURATION = 30.0  # 最大音频时长（秒）
MIN_DURATION = 1.0   # 最小音频时长（秒）

# 评估配置
BATCH_SIZE = 32  # 批处理大小
MAX_TRIALS = None  # 最大试验数量，None表示使用全部
EMBEDDING_DIM = 512  # 说话人嵌入维度

# Speaker Verification试验生成配置
MIN_UTTERANCES_PER_SPEAKER = 3  # 每个说话人最少音频数量

# 快速验证模式 - 设置为True可以快速验证代码
QUICK_VALIDATION_MODE = False  # 设置为True启用快速验证模式

# 正常模式配置
MAX_SAME_SPEAKER_TRIALS = 50  # 每个说话人最多同说话人试验数
MAX_DIFF_SPEAKER_TRIALS = 100  # 每个说话人最多不同说话人试验数

# 快速验证模式配置（用于快速测试）
QUICK_MAX_SAME_SPEAKER_TRIALS = 5   # 快速模式：每个说话人最多同说话人试验数
QUICK_MAX_DIFF_SPEAKER_TRIALS = 10  # 快速模式：每个说话人最多不同说话人试验数
QUICK_MAX_SPEAKERS = 20             # 快速模式：最多处理的说话人数量

TRAIN_TEST_SPLIT_RATIO = 0.7  # 训练/测试分割比例（用于注册/测试分割）

# 输出配置
SV_OUTPUT_DIR = "./sv_results_aishell"
SV_RESULTS_FILE = "sv_evaluation_results_aishell.json"
SV_DETAILED_RESULTS_FILE = "sv_detailed_results_aishell.csv"
SV_EMBEDDINGS_FILE = "speaker_embeddings_aishell.npz"

# 评估指标配置
# EER计算配置
EER_THRESHOLD_STEP = 0.001  # EER计算的阈值步长

# MinDCF计算配置
DCF_P_TARGET = 0.01  # 目标说话人先验概率
DCF_C_MISS = 1.0     # 漏检代价
DCF_C_FA = 1.0       # 误检代价

# 数据预处理配置
NORMALIZE_AUDIO = True  # 是否标准化音频
REMOVE_SILENCE = False  # 是否移除静音段
