"""
增加AISHELL-1 Speaker Verification验证数据量的脚本
"""

import os
import argparse


def update_config_values(max_same_trials=None, max_diff_trials=None, quick_mode=None):
    """更新配置文件中的验证数据量设置"""
    config_file = "sv_config_aishell.py"
    
    if not os.path.exists(config_file):
        print(f"Configuration file not found: {config_file}")
        return False
    
    # 读取配置文件
    with open(config_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 更新设置
    if max_same_trials is not None:
        # 更新正常模式的同说话人试验数
        import re
        pattern = r'MAX_SAME_SPEAKER_TRIALS\s*=\s*\d+'
        replacement = f'MAX_SAME_SPEAKER_TRIALS = {max_same_trials}'
        content = re.sub(pattern, replacement, content)
    
    if max_diff_trials is not None:
        # 更新正常模式的不同说话人试验数
        import re
        pattern = r'MAX_DIFF_SPEAKER_TRIALS\s*=\s*\d+'
        replacement = f'MAX_DIFF_SPEAKER_TRIALS = {max_diff_trials}'
        content = re.sub(pattern, replacement, content)
    
    if quick_mode is not None:
        # 更新快速验证模式
        if quick_mode:
            content = content.replace("QUICK_VALIDATION_MODE = False", "QUICK_VALIDATION_MODE = True")
        else:
            content = content.replace("QUICK_VALIDATION_MODE = True", "QUICK_VALIDATION_MODE = False")
    
    # 如果内容有变化，写回文件
    if content != original_content:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Configuration updated successfully")
        return True
    else:
        print("ℹ️  No changes needed")
        return True


def show_current_config():
    """显示当前配置"""
    try:
        import sv_config_aishell
        print("Current Configuration:")
        print(f"  Quick validation mode: {sv_config_aishell.QUICK_VALIDATION_MODE}")
        print(f"  Max same speaker trials: {sv_config_aishell.MAX_SAME_SPEAKER_TRIALS}")
        print(f"  Max diff speaker trials: {sv_config_aishell.MAX_DIFF_SPEAKER_TRIALS}")
        
        if sv_config_aishell.QUICK_VALIDATION_MODE:
            print(f"  Quick mode - Max speakers: {sv_config_aishell.QUICK_MAX_SPEAKERS}")
            print(f"  Quick mode - Max same trials: {sv_config_aishell.QUICK_MAX_SAME_SPEAKER_TRIALS}")
            print(f"  Quick mode - Max diff trials: {sv_config_aishell.QUICK_MAX_DIFF_SPEAKER_TRIALS}")
        
        return True
    except Exception as e:
        print(f"Error reading configuration: {e}")
        return False


def estimate_trials(max_same, max_diff, num_speakers=None):
    """估算试验数量"""
    if num_speakers is None:
        # 假设有400个说话人（AISHELL-1的大概数量）
        num_speakers = 400
    
    total_same_trials = num_speakers * max_same
    total_diff_trials = num_speakers * max_diff
    total_trials = total_same_trials + total_diff_trials
    
    print(f"\n📊 Estimated trials (assuming {num_speakers} speakers):")
    print(f"  Same speaker trials: {total_same_trials:,}")
    print(f"  Different speaker trials: {total_diff_trials:,}")
    print(f"  Total trials: {total_trials:,}")
    
    # 估算时间（基于之前的性能）
    # 假设每个trial需要0.1秒（包括嵌入提取和相似度计算）
    estimated_time_seconds = total_trials * 0.1
    estimated_time_minutes = estimated_time_seconds / 60
    estimated_time_hours = estimated_time_minutes / 60
    
    print(f"\n⏱️  Estimated evaluation time:")
    if estimated_time_hours >= 1:
        print(f"  Approximately {estimated_time_hours:.1f} hours")
    elif estimated_time_minutes >= 1:
        print(f"  Approximately {estimated_time_minutes:.1f} minutes")
    else:
        print(f"  Approximately {estimated_time_seconds:.1f} seconds")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Increase AISHELL-1 Speaker Verification validation data")
    parser.add_argument("--same-trials", type=int, 
                       help="Max same speaker trials per speaker (default: current setting)")
    parser.add_argument("--diff-trials", type=int,
                       help="Max different speaker trials per speaker (default: current setting)")
    parser.add_argument("--quick-mode", action="store_true",
                       help="Enable quick validation mode")
    parser.add_argument("--normal-mode", action="store_true",
                       help="Disable quick validation mode")
    parser.add_argument("--show-config", action="store_true",
                       help="Show current configuration")
    parser.add_argument("--preset", choices=["small", "medium", "large", "xlarge"],
                       help="Use preset configurations")
    
    args = parser.parse_args()
    
    print("AISHELL-1 Speaker Verification Data Configuration")
    print("="*60)
    
    # 显示当前配置
    if args.show_config or (not any([args.same_trials, args.diff_trials, args.quick_mode, args.normal_mode, args.preset])):
        show_current_config()
        return
    
    # 预设配置
    if args.preset:
        presets = {
            "small": {"same": 10, "diff": 20},      # ~12K trials
            "medium": {"same": 25, "diff": 50},     # ~30K trials  
            "large": {"same": 50, "diff": 100},     # ~60K trials (当前默认)
            "xlarge": {"same": 100, "diff": 200}    # ~120K trials
        }
        
        preset = presets[args.preset]
        args.same_trials = preset["same"]
        args.diff_trials = preset["diff"]
        print(f"Using preset '{args.preset}':")
        print(f"  Same speaker trials: {args.same_trials}")
        print(f"  Different speaker trials: {args.diff_trials}")
    
    # 确定快速模式设置
    quick_mode = None
    if args.quick_mode:
        quick_mode = True
    elif args.normal_mode:
        quick_mode = False
    
    # 估算试验数量
    if args.same_trials or args.diff_trials:
        same_trials = args.same_trials or 50  # 默认值
        diff_trials = args.diff_trials or 100  # 默认值
        estimate_trials(same_trials, diff_trials)
    
    # 更新配置
    if update_config_values(args.same_trials, args.diff_trials, quick_mode):
        print("\n✅ Configuration updated!")
        print("\nNext steps:")
        if quick_mode:
            print("1. Run quick validation: python quick_validation_aishell.py")
        else:
            print("1. Run full evaluation: python sv_evaluation_aishell.py")
        print("2. Monitor progress and results")
    else:
        print("\n❌ Failed to update configuration")
    
    # 显示更新后的配置
    print("\n" + "="*60)
    show_current_config()


if __name__ == "__main__":
    main()
