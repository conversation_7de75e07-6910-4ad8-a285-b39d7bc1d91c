"""
AISHELL-1数据集Speaker Verification准备脚本
为AISHELL-1数据集创建Speaker Verification评估文件
"""

import os
import argparse
from typing import Dict
import sv_config_aishell
from sv_data_loader_aishell import <PERSON>shellSVDataLoader


def check_aishell_dataset(data_path: str) -> bool:
    """检查AISHELL-1数据集是否存在且完整"""
    print("Checking AISHELL-1 dataset...")
    
    # 检查必要的文件和目录
    required_paths = [
        sv_config_aishell.AISHELL_WAV_PATH,
        sv_config_aishell.AISHELL_TRANSCRIPT_PATH,
    ]
    
    missing_paths = []
    for path in required_paths:
        full_path = os.path.join(data_path, path)
        if not os.path.exists(full_path):
            missing_paths.append(path)
        else:
            print(f"✓ Found: {path}")
    
    if missing_paths:
        print("✗ Missing required files/directories:")
        for path in missing_paths:
            print(f"  - {path}")
        return False
    
    # 检查音频文件数量
    wav_dir = os.path.join(data_path, sv_config_aishell.AISHELL_WAV_PATH)
    audio_count = 0
    for root, dirs, files in os.walk(wav_dir):
        audio_count += len([f for f in files if f.endswith('.wav')])
    
    print(f"Found {audio_count} audio files")
    
    if audio_count == 0:
        print("✗ No audio files found")
        return False
    
    # 检查转录文件
    transcript_path = os.path.join(data_path, sv_config_aishell.AISHELL_TRANSCRIPT_PATH)
    with open(transcript_path, 'r', encoding='utf-8') as f:
        transcript_count = len(f.readlines())
    
    print(f"Found {transcript_count} transcripts")
    
    if transcript_count == 0:
        print("✗ No transcripts found")
        return False
    
    print("✅ AISHELL-1 dataset check passed")
    return True


def create_sv_evaluation_files(data_path: str, force: bool = False) -> bool:
    """创建Speaker Verification评估文件"""
    
    # 检查输出目录
    sv_eval_dir = os.path.join(data_path, sv_config_aishell.SV_EVAL_DIR)
    trials_path = os.path.join(sv_eval_dir, "trials.lst")
    
    if os.path.exists(trials_path) and not force:
        print(f"Speaker verification files already exist in {sv_eval_dir}")
        print("Use --force to regenerate")
        return True
    
    print("Creating speaker verification evaluation files...")
    
    try:
        # 创建数据加载器
        data_loader = AishellSVDataLoader(
            data_path=data_path,
            sample_rate=sv_config_aishell.SAMPLE_RATE
        )
        
        # 获取评估数据
        eval_data = data_loader.get_evaluation_data()
        
        # 保存评估文件
        data_loader.save_evaluation_files(eval_data, sv_eval_dir)
        
        # 显示统计信息
        print("\n📊 Dataset Statistics:")
        info = eval_data['dataset_info']
        print(f"  Total utterances: {info['total_utterances']}")
        print(f"  Valid utterances: {info['valid_utterances']}")
        print(f"  Total speakers: {info['total_speakers']}")
        print(f"  Total trials: {info['total_trials']}")
        print(f"  Enrollment audios: {info['enroll_count']}")
        print(f"  Test audios: {info['test_count']}")
        
        # 计算试验分布
        same_trials = sum(1 for t in eval_data['trials'] if t['label'] == 1)
        diff_trials = sum(1 for t in eval_data['trials'] if t['label'] == 0)
        print(f"  Same speaker trials: {same_trials}")
        print(f"  Different speaker trials: {diff_trials}")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to create evaluation files: {e}")
        import traceback
        traceback.print_exc()
        return False


def update_config_suggestion(data_path: str):
    """显示配置更新建议"""
    print(f"\n📝 Configuration Update:")
    print(f"Make sure sv_config_aishell.py has the correct path:")
    print(f"AISHELL_DATA_PATH = '{data_path}'")
    
    current_path = sv_config_aishell.AISHELL_DATA_PATH
    if current_path != data_path:
        print(f"\n⚠️  Current config path: {current_path}")
        print(f"   Suggested path: {data_path}")
        print("   Please update sv_config_aishell.py if needed")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Prepare AISHELL-1 dataset for Speaker Verification")
    parser.add_argument("--data-path", type=str, default="./data/aishell",
                       help="Path to AISHELL-1 dataset (default: ./data/aishell)")
    parser.add_argument("--force", action="store_true",
                       help="Force regenerate evaluation files even if they exist")
    
    args = parser.parse_args()
    
    print("AISHELL-1 Speaker Verification Preparation")
    print("=" * 50)
    
    data_path = os.path.abspath(args.data_path)
    print(f"Dataset path: {data_path}")
    
    # 检查数据集
    if not os.path.exists(data_path):
        print(f"❌ Dataset path does not exist: {data_path}")
        print("\nPlease ensure AISHELL-1 dataset is downloaded and extracted.")
        print("You can download it from: https://www.openslr.org/33/")
        return
    
    if not check_aishell_dataset(data_path):
        print("❌ Dataset check failed")
        print("\nPlease ensure AISHELL-1 dataset is properly downloaded and extracted.")
        return
    
    # 创建评估文件
    if create_sv_evaluation_files(data_path, force=args.force):
        print("\n✅ Speaker verification preparation completed successfully!")
        print("\nNext steps:")
        print("1. Update sv_config_aishell.py with correct dataset path if needed")
        print("2. Run: python sv_evaluation_aishell.py")
        
        update_config_suggestion(data_path)
    else:
        print("\n❌ Speaker verification preparation failed")
        print("Please check the error messages above")


if __name__ == "__main__":
    main()
