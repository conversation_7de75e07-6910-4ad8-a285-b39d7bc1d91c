"""
Speaker Verification测试脚本
测试环境配置和基本功能
"""

import os
import numpy as np
import json
from datetime import datetime

def test_imports():
    """测试基本导入"""
    print("Testing imports...")

    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch: {e}")
        return False

    try:
        from modelscope.pipelines import pipeline
        print("✓ ModelScope")
    except ImportError as e:
        print(f"✗ ModelScope: {e}")
        return False

    try:
        import sv_config
        from sv_data_loader import CNCeleb1DataLoader
        from sv_metrics import SVMetrics
        print("✓ SV modules")
    except ImportError as e:
        print(f"✗ SV modules: {e}")
        return False

    return True

def test_metrics():
    """测试评估指标"""
    print("Testing metrics...")

    try:
        from sv_metrics import SVMetrics

        metrics = SVMetrics()

        # 创建测试数据
        np.random.seed(42)
        scores = np.random.randn(100).tolist()
        labels = (np.random.rand(100) > 0.5).astype(int).tolist()

        # 测试指标计算
        eer, _ = metrics.calculate_eer(scores, labels)
        mindcf, _ = metrics.calculate_mindcf(scores, labels)
        accuracy = metrics.calculate_accuracy(scores, labels)

        print(f"✓ EER: {eer:.4f}")
        print(f"✓ MinDCF: {mindcf:.4f}")
        print(f"✓ Accuracy: {accuracy:.4f}")

        return True

    except Exception as e:
        print(f"✗ Metrics test failed: {e}")
        return False

def test_data_loader():
    """测试数据加载器"""
    print("Testing data loader...")

    try:
        from sv_data_loader import CNCeleb1DataLoader

        loader = CNCeleb1DataLoader("./dummy", sample_rate=16000)

        # 测试虚拟trials创建
        trials = loader.create_dummy_trials(num_trials=10)
        print(f"✓ Created {len(trials)} dummy trials")

        return True

    except Exception as e:
        print(f"✗ Data loader test failed: {e}")
        return False

def run_demo():
    """运行演示"""
    print("Running speaker verification demo...")

    import sv_config
    from sv_metrics import SVMetrics

    # 创建输出目录
    os.makedirs(sv_config.SV_OUTPUT_DIR, exist_ok=True)

    # 模拟说话人验证结果
    np.random.seed(42)

    # 创建模拟数据：50个同说话人试验，50个不同说话人试验
    scores = []
    labels = []

    # 同说话人试验（高相似度）
    target_scores = np.random.normal(0.7, 0.15, 50)
    scores.extend(target_scores.tolist())
    labels.extend([1] * 50)

    # 不同说话人试验（低相似度）
    nontarget_scores = np.random.normal(0.3, 0.15, 50)
    scores.extend(nontarget_scores.tolist())
    labels.extend([0] * 50)

    # 计算评估指标
    metrics = SVMetrics()
    evaluation_metrics = metrics.calculate_all_metrics(scores, labels)

    # 显示结果
    metrics.print_metrics_summary(evaluation_metrics)

    # 保存结果
    results_data = {
        'metrics': evaluation_metrics,
        'evaluation_info': {
            'model_id': sv_config.SV_MODEL_ID,
            'dataset': 'Demo Data',
            'total_trials': len(scores),
            'evaluation_time': datetime.now().isoformat(),
            'note': 'Demonstration with simulated data'
        }
    }

    results_file = os.path.join(sv_config.SV_OUTPUT_DIR, "demo_results.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(results_data, f, ensure_ascii=False, indent=2)

    print(f"\nResults saved to: {results_file}")

    return True

def main():
    """主函数"""
    print("Speaker Verification Test")
    print("="*50)

    tests = [
        ("Imports", test_imports),
        ("Metrics", test_metrics),
        ("Data Loader", test_data_loader),
        ("Demo", run_demo),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                print(f"✅ {test_name} PASSED")
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED: {e}")

    print(f"\n{'='*50}")
    print(f"Test Results: {passed}/{total} passed")

    if passed == total:
        print("🎉 All tests passed!")
        print("\nNext steps:")
        print("1. Download CN-Celeb1: python prepare_cnceleb1.py")
        print("2. Update sv_config.py with dataset path")
        print("3. Run evaluation: python sv_evaluation.py")
    else:
        print("⚠️  Some tests failed. Check error messages above.")

    print("="*50)

if __name__ == "__main__":
    main()
