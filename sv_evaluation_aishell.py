"""
AISHELL-1数据集Speaker Verification评估主脚本
使用iic/speech_campplus_sv_zh-cn_16k-common模型
在AISHELL-1数据集上进行说话人验证准确率验证
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import List, Dict, Tuple
import torch
from funasr import AutoModel
from tqdm import tqdm

from sv_data_loader_aishell import AishellSVDataLoader
from sv_metrics import SVMetrics
import sv_config_aishell


class AishellSVEvaluator:
    """AISHELL-1 Speaker Verification评估器"""

    def __init__(self):
        self.device = sv_config_aishell.DEVICE if torch.cuda.is_available() else "cpu"
        print(f"Using device: {self.device}")

        # 初始化模型
        print("Loading Speaker Verification model...")
        try:
            # 使用 FunASR 的 AutoModel
            self.sv_model = AutoModel(
                model=sv_config_aishell.SV_MODEL_ID,
                disable_update=True  # 禁用更新检查以加快加载速度
            )
            print("Model loaded successfully!")
        except Exception as e:
            print(f"Error loading model: {e}")
            print("Trying alternative loading method...")
            # 备用加载方法，使用简化的模型名称
            try:
                self.sv_model = AutoModel(
                    model="cam++",
                    disable_update=True
                )
                print("Model loaded with alternative method!")
            except Exception as e2:
                print(f"Alternative method also failed: {e2}")
                raise e2

        # 初始化数据加载器和指标计算器
        self.data_loader = AishellSVDataLoader(
            data_path=sv_config_aishell.AISHELL_DATA_PATH,
            sample_rate=sv_config_aishell.SAMPLE_RATE
        )
        self.metrics = SVMetrics()

        # 创建输出目录
        os.makedirs(sv_config_aishell.SV_OUTPUT_DIR, exist_ok=True)

    def extract_embedding(self, audio_path: str) -> np.ndarray:
        """提取单个音频的说话人嵌入"""
        try:
            # 处理输入数据格式
            if not os.path.isabs(audio_path):
                audio_path = os.path.join(sv_config_aishell.AISHELL_DATA_PATH, audio_path)

            if not os.path.exists(audio_path):
                print(f"Warning: Audio file not found: {audio_path}")
                return None

            # 使用 FunASR AutoModel 提取嵌入
            result = self.sv_model.generate(input=audio_path)

            # 处理结果 - 基于调试得到的实际格式
            if isinstance(result, list) and len(result) > 0:
                result_dict = result[0]
                if isinstance(result_dict, dict) and 'spk_embedding' in result_dict:
                    embedding = result_dict['spk_embedding']
                else:
                    print(f"Warning: No 'spk_embedding' found in result keys: {list(result_dict.keys())}")
                    return None
            else:
                print(f"Warning: Unexpected model output format for {audio_path}")
                print(f"Result type: {type(result)}")
                print(f"Expected: list with dict containing 'spk_embedding'")
                return None

            # 确保返回numpy数组
            if hasattr(embedding, 'cpu'):  # PyTorch tensor
                embedding = embedding.cpu().numpy()
            elif isinstance(embedding, list):
                embedding = np.array(embedding)
            elif not isinstance(embedding, np.ndarray):
                embedding = np.array(embedding)

            # 确保是1D数组
            if len(embedding.shape) > 1:
                embedding = embedding.flatten()

            # 标准化嵌入向量
            embedding = embedding / (np.linalg.norm(embedding) + 1e-8)

            return embedding

        except Exception as e:
            print(f"Error extracting embedding from {audio_path}: {e}")
            import traceback
            traceback.print_exc()
            return None

    def extract_all_embeddings(self, eval_data: Dict) -> Tuple[Dict, Dict]:
        """提取所有音频的嵌入向量"""
        print("Extracting speaker embeddings...")

        enroll_embeddings = {}
        test_embeddings = {}

        # 提取注册音频嵌入
        print("Extracting enrollment embeddings...")
        for audio_id, audio_path in tqdm(eval_data['enroll_audios'].items(),
                                       desc="Enrollment"):
            embedding = self.extract_embedding(audio_path)
            if embedding is not None:
                enroll_embeddings[audio_id] = embedding

        # 提取测试音频嵌入
        print("Extracting test embeddings...")
        for audio_id, audio_path in tqdm(eval_data['test_audios'].items(),
                                       desc="Test"):
            embedding = self.extract_embedding(audio_path)
            if embedding is not None:
                test_embeddings[audio_id] = embedding

        print(f"Successfully extracted {len(enroll_embeddings)} enrollment embeddings")
        print(f"Successfully extracted {len(test_embeddings)} test embeddings")

        return enroll_embeddings, test_embeddings

    def compute_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """计算两个嵌入向量的余弦相似度"""
        try:
            # 余弦相似度
            similarity = np.dot(embedding1, embedding2) / (
                np.linalg.norm(embedding1) * np.linalg.norm(embedding2) + 1e-8
            )
            return float(similarity)
        except Exception as e:
            print(f"Error computing similarity: {e}")
            return 0.0

    def evaluate_trials(self, eval_data: Dict) -> Dict:
        """评估所有试验"""
        print("Starting speaker verification evaluation...")

        # 提取所有嵌入
        enroll_embeddings, test_embeddings = self.extract_all_embeddings(eval_data)

        # 计算相似度分数
        print("Computing similarity scores...")
        scores = []
        labels = []
        detailed_results = []

        valid_trials = 0
        for trial in tqdm(eval_data['trials'], desc="Computing scores"):
            enroll_id = trial['enroll_id']
            test_id = trial['test_id']
            label = trial['label']

            # 检查嵌入是否存在
            if enroll_id not in enroll_embeddings or test_id not in test_embeddings:
                continue

            # 计算相似度
            enroll_emb = enroll_embeddings[enroll_id]
            test_emb = test_embeddings[test_id]
            score = self.compute_similarity(enroll_emb, test_emb)

            scores.append(score)
            labels.append(label)
            valid_trials += 1

            # 记录详细结果
            detailed_results.append({
                'enroll_id': enroll_id,
                'test_id': test_id,
                'label': label,
                'score': score,
                'speaker_id': trial.get('speaker_id', ''),
                'other_speaker_id': trial.get('other_speaker_id', '')
            })

        print(f"Computed scores for {valid_trials} valid trials")

        if len(scores) == 0:
            raise ValueError("No valid trials found for evaluation")

        # 计算评估指标
        print("Calculating evaluation metrics...")
        metrics = self.metrics.calculate_all_metrics(scores, labels)

        return {
            'metrics': metrics,
            'detailed_results': detailed_results,
            'embeddings': {
                'enroll_embeddings': enroll_embeddings,
                'test_embeddings': test_embeddings
            },
            'evaluation_info': {
                'model_id': sv_config_aishell.SV_MODEL_ID,
                'dataset': 'AISHELL-1',
                'total_trials': len(eval_data['trials']),
                'valid_trials': len(scores),
                'evaluation_time': datetime.now().isoformat(),
                'device': self.device,
                'config': {
                    'sample_rate': sv_config_aishell.SAMPLE_RATE,
                    'max_duration': sv_config_aishell.MAX_DURATION,
                    'min_duration': sv_config_aishell.MIN_DURATION,
                    'normalize_audio': sv_config_aishell.NORMALIZE_AUDIO
                }
            }
        }

    def save_results(self, results: Dict):
        """保存评估结果"""
        print("Saving evaluation results...")

        # 保存总体结果
        results_file = os.path.join(sv_config_aishell.SV_OUTPUT_DIR,
                                  sv_config_aishell.SV_RESULTS_FILE)
        with open(results_file, 'w', encoding='utf-8') as f:
            # 移除不能JSON序列化的numpy数组
            save_results = {
                'metrics': results['metrics'],
                'evaluation_info': results['evaluation_info']
            }
            json.dump(save_results, f, indent=2, ensure_ascii=False)

        # 保存详细结果
        detailed_file = os.path.join(sv_config_aishell.SV_OUTPUT_DIR,
                                   sv_config_aishell.SV_DETAILED_RESULTS_FILE)
        df = pd.DataFrame(results['detailed_results'])
        df.to_csv(detailed_file, index=False, encoding='utf-8')

        # 保存嵌入向量
        embeddings_file = os.path.join(sv_config_aishell.SV_OUTPUT_DIR,
                                     sv_config_aishell.SV_EMBEDDINGS_FILE)
        np.savez_compressed(embeddings_file,
                          enroll_embeddings=results['embeddings']['enroll_embeddings'],
                          test_embeddings=results['embeddings']['test_embeddings'])

        print(f"Results saved to {sv_config_aishell.SV_OUTPUT_DIR}")

    def print_summary(self, results: Dict):
        """打印评估结果摘要"""
        print("\n" + "="*60)
        print("AISHELL-1 SPEAKER VERIFICATION EVALUATION RESULTS")
        print("="*60)

        metrics = results['metrics']
        info = results['evaluation_info']

        print(f"Model: {info['model_id']}")
        print(f"Dataset: {info['dataset']}")
        print(f"Device: {info['device']}")
        print(f"Evaluation Time: {info['evaluation_time']}")
        print(f"Valid Trials: {info['valid_trials']}")

        print("\n📊 Performance Metrics:")
        print(f"  EER (Equal Error Rate): {metrics['eer']:.4f} ({metrics['eer']*100:.2f}%)")

        # 修复键名问题 - sv_metrics.py中使用的是'mindcf'而不是'min_dcf'
        if 'mindcf' in metrics:
            print(f"  MinDCF: {metrics['mindcf']:.6f}")
        elif 'min_dcf' in metrics:
            print(f"  MinDCF: {metrics['min_dcf']:.6f}")

        # 显示更多指标
        if 'accuracy_at_eer' in metrics:
            print(f"  Accuracy at EER: {metrics['accuracy_at_eer']:.4f} ({metrics['accuracy_at_eer']*100:.2f}%)")

        if 'accuracy_at_mindcf' in metrics:
            print(f"  Accuracy at MinDCF: {metrics['accuracy_at_mindcf']:.4f} ({metrics['accuracy_at_mindcf']*100:.2f}%)")

        if 'accuracy_at_0.5' in metrics:
            print(f"  Accuracy at 0.5: {metrics['accuracy_at_0.5']:.4f} ({metrics['accuracy_at_0.5']*100:.2f}%)")

        if 'accuracy_at_thresholds' in metrics:
            best_acc = max(metrics['accuracy_at_thresholds'])
            print(f"  Best Accuracy: {best_acc:.4f} ({best_acc*100:.2f}%)")

        # 显示分数统计
        if 'score_statistics' in metrics:
            stats = metrics['score_statistics']
            print(f"\n📈 Score Statistics:")
            if 'target_scores' in stats:
                target_stats = stats['target_scores']
                print(f"  Target (same speaker) scores: mean={target_stats['mean']:.4f}, std={target_stats['std']:.4f}, count={target_stats['count']}")
            if 'nontarget_scores' in stats:
                nontarget_stats = stats['nontarget_scores']
                print(f"  Non-target (diff speaker) scores: mean={nontarget_stats['mean']:.4f}, std={nontarget_stats['std']:.4f}, count={nontarget_stats['count']}")

        # 显示混淆矩阵
        if 'confusion_matrix' in metrics:
            cm = metrics['confusion_matrix']
            print(f"\n🔢 Confusion Matrix (at EER threshold):")
            print(f"  True Positive: {cm['true_positive']}")
            print(f"  True Negative: {cm['true_negative']}")
            print(f"  False Positive: {cm['false_positive']}")
            print(f"  False Negative: {cm['false_negative']}")

        print(f"\n📁 Output Files:")
        print(f"  Results: {sv_config_aishell.SV_OUTPUT_DIR}/{sv_config_aishell.SV_RESULTS_FILE}")
        print(f"  Details: {sv_config_aishell.SV_OUTPUT_DIR}/{sv_config_aishell.SV_DETAILED_RESULTS_FILE}")
        print(f"  Embeddings: {sv_config_aishell.SV_OUTPUT_DIR}/{sv_config_aishell.SV_EMBEDDINGS_FILE}")

        print("="*60)


def main():
    """主函数"""
    try:
        print("AISHELL-1 Speaker Verification Evaluation")
        print("="*50)

        # 检查数据集路径
        if not os.path.exists(sv_config_aishell.AISHELL_DATA_PATH):
            print(f"Error: Dataset path not found: {sv_config_aishell.AISHELL_DATA_PATH}")
            print("Please ensure AISHELL-1 dataset is properly downloaded and configured.")
            print("You can run: python prepare_aishell_sv.py")
            return

        # 检查评估文件
        sv_eval_dir = os.path.join(sv_config_aishell.AISHELL_DATA_PATH, sv_config_aishell.SV_EVAL_DIR)
        trials_path = os.path.join(sv_eval_dir, "trials.lst")

        if not os.path.exists(trials_path):
            print(f"Error: Speaker verification files not found in {sv_eval_dir}")
            print("Please run: python prepare_aishell_sv.py")
            return

        # 创建评估器
        evaluator = AishellSVEvaluator()

        # 加载评估数据
        print("Loading evaluation data...")
        eval_data = evaluator.data_loader.get_evaluation_data()

        # 运行评估
        results = evaluator.evaluate_trials(eval_data)

        # 保存和显示结果
        evaluator.save_results(results)
        evaluator.print_summary(results)

    except Exception as e:
        print(f"Evaluation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()