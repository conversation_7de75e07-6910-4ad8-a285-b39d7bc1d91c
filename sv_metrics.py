"""
Speaker Verification评估指标计算器
包括EER (Equal Error Rate)和MinDCF (Minimum Detection Cost Function)
"""

import numpy as np
from typing import List, Tuple, Dict
from sklearn.metrics import roc_curve
import sv_config


class SVMetrics:
    """Speaker Verification评估指标计算器"""
    
    def __init__(self):
        self.p_target = sv_config.DCF_P_TARGET
        self.c_miss = sv_config.DCF_C_MISS
        self.c_fa = sv_config.DCF_C_FA
    
    def calculate_eer(self, scores: List[float], labels: List[int]) -> Tuple[float, float]:
        """
        计算Equal Error Rate (EER)
        
        Args:
            scores: 相似度分数列表
            labels: 标签列表 (1表示同一说话人，0表示不同说话人)
            
        Returns:
            eer: Equal Error Rate
            threshold: EER对应的阈值
        """
        if len(scores) != len(labels):
            raise ValueError("Scores and labels must have the same length")
        
        # 转换为numpy数组
        scores = np.array(scores)
        labels = np.array(labels)
        
        # 计算ROC曲线
        fpr, tpr, thresholds = roc_curve(labels, scores, pos_label=1)
        
        # 计算False Negative Rate (FNR = 1 - TPR)
        fnr = 1 - tpr
        
        # 找到FPR和FNR最接近的点
        eer_idx = np.argmin(np.abs(fpr - fnr))
        eer = (fpr[eer_idx] + fnr[eer_idx]) / 2
        eer_threshold = thresholds[eer_idx]
        
        return float(eer), float(eer_threshold)
    
    def calculate_mindcf(self, scores: List[float], labels: List[int]) -> Tuple[float, float]:
        """
        计算Minimum Detection Cost Function (MinDCF)
        
        Args:
            scores: 相似度分数列表
            labels: 标签列表 (1表示同一说话人，0表示不同说话人)
            
        Returns:
            mindcf: Minimum DCF
            threshold: MinDCF对应的阈值
        """
        if len(scores) != len(labels):
            raise ValueError("Scores and labels must have the same length")
        
        # 转换为numpy数组
        scores = np.array(scores)
        labels = np.array(labels)
        
        # 计算ROC曲线
        fpr, tpr, thresholds = roc_curve(labels, scores, pos_label=1)
        
        # 计算False Negative Rate
        fnr = 1 - tpr
        
        # 计算DCF
        dcf = self.c_miss * fnr * self.p_target + self.c_fa * fpr * (1 - self.p_target)
        
        # 找到最小DCF
        mindcf_idx = np.argmin(dcf)
        mindcf = dcf[mindcf_idx]
        mindcf_threshold = thresholds[mindcf_idx]
        
        return float(mindcf), float(mindcf_threshold)
    
    def calculate_accuracy(self, scores: List[float], labels: List[int], 
                         threshold: float = 0.5) -> float:
        """
        计算给定阈值下的准确率
        
        Args:
            scores: 相似度分数列表
            labels: 标签列表
            threshold: 判决阈值
            
        Returns:
            accuracy: 准确率
        """
        if len(scores) != len(labels):
            raise ValueError("Scores and labels must have the same length")
        
        scores = np.array(scores)
        labels = np.array(labels)
        
        # 根据阈值进行判决
        predictions = (scores >= threshold).astype(int)
        
        # 计算准确率
        accuracy = np.mean(predictions == labels)
        
        return float(accuracy)
    
    def calculate_confusion_matrix(self, scores: List[float], labels: List[int], 
                                 threshold: float = 0.5) -> Dict[str, int]:
        """
        计算混淆矩阵
        
        Args:
            scores: 相似度分数列表
            labels: 标签列表
            threshold: 判决阈值
            
        Returns:
            confusion_matrix: 包含TP, TN, FP, FN的字典
        """
        if len(scores) != len(labels):
            raise ValueError("Scores and labels must have the same length")
        
        scores = np.array(scores)
        labels = np.array(labels)
        
        # 根据阈值进行判决
        predictions = (scores >= threshold).astype(int)
        
        # 计算混淆矩阵
        tp = np.sum((predictions == 1) & (labels == 1))
        tn = np.sum((predictions == 0) & (labels == 0))
        fp = np.sum((predictions == 1) & (labels == 0))
        fn = np.sum((predictions == 0) & (labels == 1))
        
        return {
            'true_positive': int(tp),
            'true_negative': int(tn),
            'false_positive': int(fp),
            'false_negative': int(fn)
        }
    
    def calculate_all_metrics(self, scores: List[float], labels: List[int]) -> Dict:
        """
        计算所有评估指标
        
        Args:
            scores: 相似度分数列表
            labels: 标签列表
            
        Returns:
            metrics: 包含所有指标的字典
        """
        if len(scores) != len(labels):
            raise ValueError("Scores and labels must have the same length")
        
        # 计算EER
        eer, eer_threshold = self.calculate_eer(scores, labels)
        
        # 计算MinDCF
        mindcf, mindcf_threshold = self.calculate_mindcf(scores, labels)
        
        # 计算不同阈值下的准确率
        accuracy_eer = self.calculate_accuracy(scores, labels, eer_threshold)
        accuracy_mindcf = self.calculate_accuracy(scores, labels, mindcf_threshold)
        accuracy_05 = self.calculate_accuracy(scores, labels, 0.5)
        
        # 计算混淆矩阵（使用EER阈值）
        confusion_matrix = self.calculate_confusion_matrix(scores, labels, eer_threshold)
        
        # 计算统计信息
        scores_array = np.array(scores)
        labels_array = np.array(labels)
        
        target_scores = scores_array[labels_array == 1]  # 同说话人分数
        nontarget_scores = scores_array[labels_array == 0]  # 不同说话人分数
        
        return {
            'eer': eer,
            'eer_threshold': eer_threshold,
            'mindcf': mindcf,
            'mindcf_threshold': mindcf_threshold,
            'accuracy_at_eer': accuracy_eer,
            'accuracy_at_mindcf': accuracy_mindcf,
            'accuracy_at_0.5': accuracy_05,
            'confusion_matrix': confusion_matrix,
            'score_statistics': {
                'target_scores': {
                    'mean': float(np.mean(target_scores)) if len(target_scores) > 0 else 0.0,
                    'std': float(np.std(target_scores)) if len(target_scores) > 0 else 0.0,
                    'min': float(np.min(target_scores)) if len(target_scores) > 0 else 0.0,
                    'max': float(np.max(target_scores)) if len(target_scores) > 0 else 0.0,
                    'count': len(target_scores)
                },
                'nontarget_scores': {
                    'mean': float(np.mean(nontarget_scores)) if len(nontarget_scores) > 0 else 0.0,
                    'std': float(np.std(nontarget_scores)) if len(nontarget_scores) > 0 else 0.0,
                    'min': float(np.min(nontarget_scores)) if len(nontarget_scores) > 0 else 0.0,
                    'max': float(np.max(nontarget_scores)) if len(nontarget_scores) > 0 else 0.0,
                    'count': len(nontarget_scores)
                }
            },
            'total_trials': len(scores),
            'target_trials': int(np.sum(labels_array == 1)),
            'nontarget_trials': int(np.sum(labels_array == 0))
        }
    
    def print_metrics_summary(self, metrics: Dict):
        """打印指标摘要"""
        print("\n" + "="*60)
        print("Speaker Verification Evaluation Results")
        print("="*60)
        print(f"Total trials: {metrics['total_trials']}")
        print(f"Target trials: {metrics['target_trials']}")
        print(f"Non-target trials: {metrics['nontarget_trials']}")
        print("-"*60)
        print(f"EER: {metrics['eer']:.4f} ({metrics['eer']*100:.2f}%)")
        print(f"EER threshold: {metrics['eer_threshold']:.4f}")
        print(f"MinDCF: {metrics['mindcf']:.4f}")
        print(f"MinDCF threshold: {metrics['mindcf_threshold']:.4f}")
        print("-"*60)
        print(f"Accuracy at EER threshold: {metrics['accuracy_at_eer']:.4f} ({metrics['accuracy_at_eer']*100:.2f}%)")
        print(f"Accuracy at MinDCF threshold: {metrics['accuracy_at_mindcf']:.4f} ({metrics['accuracy_at_mindcf']*100:.2f}%)")
        print(f"Accuracy at 0.5 threshold: {metrics['accuracy_at_0.5']:.4f} ({metrics['accuracy_at_0.5']*100:.2f}%)")
        print("-"*60)
        
        cm = metrics['confusion_matrix']
        print("Confusion Matrix (at EER threshold):")
        print(f"  True Positive:  {cm['true_positive']}")
        print(f"  True Negative:  {cm['true_negative']}")
        print(f"  False Positive: {cm['false_positive']}")
        print(f"  False Negative: {cm['false_negative']}")
        print("-"*60)
        
        target_stats = metrics['score_statistics']['target_scores']
        nontarget_stats = metrics['score_statistics']['nontarget_scores']
        print("Score Statistics:")
        print(f"  Target scores:     mean={target_stats['mean']:.4f}, std={target_stats['std']:.4f}")
        print(f"  Non-target scores: mean={nontarget_stats['mean']:.4f}, std={nontarget_stats['std']:.4f}")
        print("="*60)
