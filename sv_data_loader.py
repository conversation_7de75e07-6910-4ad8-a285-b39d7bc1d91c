"""
CN-Celeb1数据集加载器
用于Speaker Verification任务
"""

import os
import librosa
import soundfile as sf
import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Optional
from tqdm import tqdm
import sv_config


class CNCeleb1DataLoader:
    """CN-Celeb1数据集加载器"""

    def __init__(self, data_path: str, sample_rate: int = 16000):
        self.data_path = data_path
        self.sample_rate = sample_rate

    def load_trials_file(self, trials_path: str) -> List[Dict]:
        """
        加载trials文件
        格式: enroll_id test_id label
        其中label: 1表示同一说话人，0表示不同说话人
        """
        trials = []
        full_path = os.path.join(self.data_path, trials_path)

        if not os.path.exists(full_path):
            raise FileNotFoundError(f"Trials file not found: {full_path}")

        with open(full_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split()
                    if len(parts) >= 3:
                        enroll_id = parts[0]
                        test_id = parts[1]
                        label = int(parts[2])
                        trials.append({
                            'enroll_id': enroll_id,
                            'test_id': test_id,
                            'label': label,
                            'line_num': line_num
                        })
                    else:
                        print(f"Warning: Invalid line format at line {line_num}: {line}")

        print(f"Loaded {len(trials)} trials from {trials_path}")
        return trials

    def load_audio_list(self, list_path: str) -> Dict[str, str]:
        """
        加载音频列表文件
        格式: audio_id audio_path
        """
        audio_dict = {}
        full_path = os.path.join(self.data_path, list_path)

        if not os.path.exists(full_path):
            raise FileNotFoundError(f"Audio list file not found: {full_path}")

        with open(full_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split(None, 1)  # 只分割第一个空格
                    if len(parts) >= 2:
                        audio_id = parts[0]
                        audio_path = parts[1]
                        audio_dict[audio_id] = audio_path
                    else:
                        print(f"Warning: Invalid line format at line {line_num}: {line}")

        print(f"Loaded {len(audio_dict)} audio files from {list_path}")
        return audio_dict

    def load_audio(self, audio_path: str) -> Tuple[Optional[np.ndarray], Optional[int]]:
        """加载音频文件"""
        try:
            # 如果是相对路径，则相对于数据集根目录
            if not os.path.isabs(audio_path):
                audio_path = os.path.join(self.data_path, audio_path)

            if not os.path.exists(audio_path):
                print(f"Warning: Audio file not found: {audio_path}")
                return None, None

            audio, sr = sf.read(audio_path)

            # 确保音频是浮点数类型
            if audio.dtype != 'float32':
                audio = audio.astype('float32')

            # 如果是立体声，转换为单声道
            if len(audio.shape) > 1:
                audio = np.mean(audio, axis=1)

            # 重采样到目标采样率
            if sr != self.sample_rate:
                audio = librosa.resample(y=audio, orig_sr=sr, target_sr=self.sample_rate)

            # 音频时长检查
            duration = len(audio) / self.sample_rate
            if duration < sv_config.MIN_DURATION:
                print(f"Warning: Audio too short ({duration:.2f}s): {audio_path}")
                return None, None

            # 截断过长的音频
            if duration > sv_config.MAX_DURATION:
                max_samples = int(sv_config.MAX_DURATION * self.sample_rate)
                audio = audio[:max_samples]

            # 音频标准化
            if sv_config.NORMALIZE_AUDIO:
                audio = audio / (np.max(np.abs(audio)) + 1e-8)

            return audio, self.sample_rate

        except Exception as e:
            print(f"Error loading audio {audio_path}: {e}")
            return None, None

    def get_evaluation_data(self, trials_path: str, enroll_path: str, test_path: str,
                          max_trials: Optional[int] = None) -> Dict:
        """获取评估数据"""
        print("Loading CN-Celeb1 evaluation data...")

        # 加载trials、enroll和test列表
        trials = self.load_trials_file(trials_path)
        enroll_dict = self.load_audio_list(enroll_path)
        test_dict = self.load_audio_list(test_path)

        if max_trials:
            trials = trials[:max_trials]
            print(f"Limited to {len(trials)} trials")

        # 收集需要的音频ID
        enroll_ids = set(trial['enroll_id'] for trial in trials)
        test_ids = set(trial['test_id'] for trial in trials)

        # 加载注册音频
        enroll_audios = {}
        print("Loading enrollment audios...")
        for enroll_id in tqdm(enroll_ids, desc="Loading enroll audios"):
            if enroll_id in enroll_dict:
                audio, sr = self.load_audio(enroll_dict[enroll_id])
                if audio is not None:
                    enroll_audios[enroll_id] = {
                        'audio': audio,
                        'sample_rate': sr,
                        'path': enroll_dict[enroll_id]
                    }

        # 加载测试音频
        test_audios = {}
        print("Loading test audios...")
        for test_id in tqdm(test_ids, desc="Loading test audios"):
            if test_id in test_dict:
                audio, sr = self.load_audio(test_dict[test_id])
                if audio is not None:
                    test_audios[test_id] = {
                        'audio': audio,
                        'sample_rate': sr,
                        'path': test_dict[test_id]
                    }

        # 过滤有效的trials
        valid_trials = []
        for trial in trials:
            if (trial['enroll_id'] in enroll_audios and
                trial['test_id'] in test_audios):
                valid_trials.append(trial)

        print(f"Successfully loaded {len(valid_trials)} valid trials")
        print(f"Enrollment audios: {len(enroll_audios)}")
        print(f"Test audios: {len(test_audios)}")

        return {
            'trials': valid_trials,
            'enroll_audios': enroll_audios,
            'test_audios': test_audios,
            'dataset_info': {
                'total_trials': len(trials),
                'valid_trials': len(valid_trials),
                'enroll_count': len(enroll_audios),
                'test_count': len(test_audios)
            }
        }

    def create_dummy_trials(self, num_trials: int = 100) -> List[Dict]:
        """创建虚拟trials用于测试（当没有真实数据时）"""
        print(f"Creating {num_trials} dummy trials for testing...")
        trials = []

        for i in range(num_trials):
            # 创建一些同说话人和不同说话人的试验
            label = 1 if i % 2 == 0 else 0  # 交替创建正负样本
            trials.append({
                'enroll_id': f"dummy_enroll_{i // 2}",
                'test_id': f"dummy_test_{i}",
                'label': label,
                'line_num': i + 1
            })

        return trials
