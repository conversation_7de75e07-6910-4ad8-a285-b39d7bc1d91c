"""
测试脚本 - 验证环境设置和基本功能
"""

import sys
import os
import torch
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks

def test_dependencies():
    """测试依赖包是否正确安装"""
    print("Testing dependencies...")
    
    try:
        import modelscope
        print(f"✓ ModelScope version: {modelscope.__version__}")
    except ImportError:
        print("✗ ModelScope not installed")
        return False
    
    try:
        import torch
        print(f"✓ PyTorch version: {torch.__version__}")
        print(f"✓ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ CUDA device count: {torch.cuda.device_count()}")
    except ImportError:
        print("✗ PyTorch not installed")
        return False
    
    try:
        import librosa
        print(f"✓ Librosa available")
    except ImportError:
        print("✗ Librosa not installed")
        return False
    
    try:
        import jiwer
        print(f"✓ Jiwer available")
    except ImportError:
        print("✗ Jiwer not installed")
        return False
    
    return True

def test_model_loading():
    """测试模型加载"""
    print("\nTesting model loading...")
    
    try:
        from config import MODEL_ID, DEVICE
        device = DEVICE if torch.cuda.is_available() else "cpu"
        
        print(f"Loading model: {MODEL_ID}")
        print(f"Using device: {device}")
        
        asr_pipeline = pipeline(
            task=Tasks.auto_speech_recognition,
            model=MODEL_ID,
            device=device
        )
        
        print("✓ Model loaded successfully!")
        return True, asr_pipeline
        
    except Exception as e:
        print(f"✗ Model loading failed: {e}")
        return False, None

def test_basic_functionality():
    """测试基本功能"""
    print("\nTesting basic functionality...")
    
    try:
        from metrics import ASRMetrics
        
        metrics = ASRMetrics()
        
        # 测试中文文本处理
        ref = "你好世界"
        hyp = "你好世界"
        
        cer = metrics.calculate_cer(ref, hyp)
        accuracy = metrics.calculate_accuracy(ref, hyp)
        
        print(f"✓ CER calculation: {cer}")
        print(f"✓ Accuracy calculation: {accuracy}")
        
        # 测试错误情况
        ref2 = "你好世界"
        hyp2 = "你好地球"
        
        cer2 = metrics.calculate_cer(ref2, hyp2)
        accuracy2 = metrics.calculate_accuracy(ref2, hyp2)
        
        print(f"✓ CER with errors: {cer2}")
        print(f"✓ Accuracy with errors: {accuracy2}")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

def test_data_loader():
    """测试数据加载器"""
    print("\nTesting data loader...")
    
    try:
        from data_loader import AishellDataLoader
        
        # 创建测试数据
        test_dir = "./test_data"
        os.makedirs(test_dir, exist_ok=True)
        
        # 创建测试wav.scp文件
        with open(os.path.join(test_dir, "wav.scp"), 'w', encoding='utf-8') as f:
            f.write("test001 /path/to/test001.wav\n")
            f.write("test002 /path/to/test002.wav\n")
        
        # 创建测试text文件
        with open(os.path.join(test_dir, "text"), 'w', encoding='utf-8') as f:
            f.write("test001 这是测试文本一\n")
            f.write("test002 这是测试文本二\n")
        
        loader = AishellDataLoader(test_dir)
        
        # 测试加载wav.scp
        wav_dict = loader.load_wav_scp(os.path.join(test_dir, "wav.scp"))
        print(f"✓ Loaded wav.scp: {len(wav_dict)} entries")
        
        # 测试加载text
        text_dict = loader.load_text_file(os.path.join(test_dir, "text"))
        print(f"✓ Loaded text: {len(text_dict)} entries")
        
        # 清理测试文件
        os.remove(os.path.join(test_dir, "wav.scp"))
        os.remove(os.path.join(test_dir, "text"))
        os.rmdir(test_dir)
        
        return True
        
    except Exception as e:
        print(f"✗ Data loader test failed: {e}")
        return False

def main():
    """主测试函数"""
    print("ASR Evaluation Setup Test")
    print("=" * 50)
    
    # 测试依赖
    if not test_dependencies():
        print("\n❌ Dependency test failed. Please install required packages:")
        print("pip install -r requirements.txt")
        return
    
    # 测试基本功能
    if not test_basic_functionality():
        print("\n❌ Basic functionality test failed.")
        return
    
    # 测试数据加载器
    if not test_data_loader():
        print("\n❌ Data loader test failed.")
        return
    
    # 测试模型加载（可选，因为需要网络下载）
    print("\nTesting model loading (this may take a while for first time)...")
    model_success, _ = test_model_loading()
    
    if model_success:
        print("\n✅ All tests passed! The setup is ready for evaluation.")
    else:
        print("\n⚠️  Basic tests passed, but model loading failed.")
        print("This might be due to network issues or insufficient resources.")
        print("You can still proceed with evaluation if the model loads successfully later.")
    
    print("\nNext steps:")
    print("1. Prepare your AISHELL-1 dataset")
    print("2. Update the paths in config.py")
    print("3. Run: python asr_evaluation.py")

if __name__ == "__main__":
    main()
