"""
测试 embedding 提取修复
"""

import os
import numpy as np
import torch
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
import sv_config
from sv_data_loader import CNCeleb1DataLoader

def test_embedding_extraction():
    """测试 embedding 提取"""
    print("Testing embedding extraction fix...")

    # 初始化模型
    print("Loading Speaker Verification model...")
    try:
        sv_pipeline = pipeline(
            task=Tasks.speaker_verification,
            model=sv_config.SV_MODEL_ID,
            device="cpu"  # 使用CPU进行测试
        )
        print("Model loaded successfully!")
    except Exception as e:
        print(f"Error loading model: {e}")
        return False

    # 创建测试音频数据
    print("Creating test audio data...")
    sample_rate = 16000
    duration = 3.0  # 3秒
    samples = int(sample_rate * duration)

    # 生成测试音频（正弦波）
    t = np.linspace(0, duration, samples)
    frequency = 440  # A4音符
    test_audio = 0.5 * np.sin(2 * np.pi * frequency * t).astype(np.float32)

    print(f"Test audio shape: {test_audio.shape}, dtype: {test_audio.dtype}")

    # 测试不同的输入格式
    test_cases = [
        ("numpy array (float32)", test_audio),
        ("numpy array (float64)", test_audio.astype(np.float64)),
        ("numpy array (2D)", test_audio.reshape(1, -1)),
    ]

    for test_name, audio_data in test_cases:
        print(f"\nTesting {test_name}...")
        try:
            # 处理输入数据格式
            if isinstance(audio_data, np.ndarray):
                # 确保是1D数组
                if len(audio_data.shape) > 1:
                    audio_data = audio_data.flatten()

                # 确保是float32类型
                if audio_data.dtype != np.float32:
                    audio_data = audio_data.astype(np.float32)

                # 确保数组是连续的
                if not audio_data.flags['C_CONTIGUOUS']:
                    audio_data = np.ascontiguousarray(audio_data)

            # 使用pipeline提取嵌入
            result = sv_pipeline(audio_data)
            print(f"✓ {test_name} - Success!")
            print(f"  Result type: {type(result)}")
            if isinstance(result, dict):
                print(f"  Result keys: {list(result.keys())}")

        except Exception as e:
            print(f"✗ {test_name} - Failed: {e}")

    return True

def test_with_real_audio():
    """使用真实音频文件测试"""
    print("\nTesting with real audio files...")

    # 检查是否有真实音频文件
    data_loader = CNCeleb1DataLoader(
        data_path=sv_config.CNCELEB1_DATA_PATH,
        sample_rate=sv_config.SAMPLE_RATE
    )

    # 查找一个音频文件进行测试
    test_audio_path = None
    cnceleb_path = os.path.join(sv_config.CNCELEB1_DATA_PATH, "CN-Celeb_flac", "data")

    if os.path.exists(cnceleb_path):
        for root, dirs, files in os.walk(cnceleb_path):
            for file in files:
                if file.endswith('.flac'):
                    test_audio_path = os.path.join(root, file)
                    # 标准化路径
                    test_audio_path = os.path.normpath(test_audio_path)
                    break
            if test_audio_path:
                break

    if not test_audio_path:
        print("No real audio files found for testing")
        return True

    print(f"Testing with audio file: {test_audio_path}")

    # 初始化模型
    try:
        sv_pipeline = pipeline(
            task=Tasks.speaker_verification,
            model=sv_config.SV_MODEL_ID,
            device="cpu"
        )
    except Exception as e:
        print(f"Error loading model: {e}")
        return False

    # 测试文件路径输入（优先测试）
    print("Testing file path input...")
    try:
        # 尝试不同的路径格式
        path_formats = [
            test_audio_path,  # 原始路径
            os.path.abspath(test_audio_path),  # 绝对路径
            test_audio_path.replace('\\', '/'),  # 正斜杠
            os.path.abspath(test_audio_path).replace('\\', '/')  # 绝对路径 + 正斜杠
        ]

        success = False
        for i, path_to_try in enumerate(path_formats):
            print(f"Trying path format {i+1}: {path_to_try}")
            try:
                result = sv_pipeline(path_to_try)
                print("✓ File path input - Success!")
                print(f"  Result type: {type(result)}")
                if isinstance(result, dict):
                    print(f"  Result keys: {list(result.keys())}")
                    for key, value in result.items():
                        if isinstance(value, np.ndarray):
                            print(f"  {key} shape: {value.shape}")
                        elif isinstance(value, torch.Tensor):
                            print(f"  {key} shape: {value.shape}")
                        else:
                            print(f"  {key}: {type(value)}")
                success = True
                break
            except Exception as e:
                print(f"  Failed with format {i+1}: {e}")
                continue

        if not success:
            print("✗ All file path formats failed")
            return False

    except Exception as e:
        print(f"✗ File path input - Failed: {e}")
        return False

    # 如果文件路径成功，再测试numpy数组
    print("Testing numpy array input...")
    try:
        # 加载音频
        audio, sr = data_loader.load_audio(test_audio_path)
        if audio is None:
            print("Failed to load audio file")
            return False

        print(f"Loaded audio shape: {audio.shape}, dtype: {audio.dtype}")

        result = sv_pipeline(audio)
        print("✓ Numpy array input - Success!")
        print(f"  Result type: {type(result)}")
        if isinstance(result, dict):
            print(f"  Result keys: {list(result.keys())}")
    except Exception as e:
        print(f"✗ Numpy array input - Failed: {e}")

    return True

def main():
    """主函数"""
    print("Speaker Verification Embedding Extraction Test")
    print("=" * 60)

    try:
        # 测试合成音频
        test_embedding_extraction()

        # 测试真实音频
        test_with_real_audio()

        print("\nTest completed!")

    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
