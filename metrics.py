# metrics.py
import jiwer
from typing import List
import editdistance  # 需要安装: pip install editdistance

class ASRMetrics:
    def __init__(self):
        pass
    
    def calculate_cer(self, reference: str, hypothesis: str) -> float:
        """计算字符错误率 (CER) - 使用编辑距离"""
        try:
            # 移除多余的空格并规范化
            ref_cleaned = self._clean_text(reference)
            hyp_cleaned = self._clean_text(hypothesis)
            
            # 转换为字符列表
            ref_chars = list(ref_cleaned)
            hyp_chars = list(hyp_cleaned)
            
            if len(ref_chars) == 0:
                return 0.0 if len(hyp_chars) == 0 else 1.0
            
            # 使用编辑距离计算 CER
            edit_distance = editdistance.eval(ref_chars, hyp_chars)
            cer = edit_distance / len(ref_chars)
            
            return min(cer, 1.0)  # CER 不应超过 1.0
            
        except Exception as e:
            print(f"Error calculating CER: {e}")
            print(f"Reference: '{reference}'")
            print(f"Hypothesis: '{hypothesis}'")
            # 使用 jiwer 作为备选方案
            try:
                ref_cleaned = self._clean_text(reference)
                hyp_cleaned = self._clean_text(hypothesis)
                return jiwer.cer(ref_cleaned, hyp_cleaned)
            except:
                return 1.0
    
    def calculate_wer(self, reference: str, hypothesis: str) -> float:
        """计算词错误率 (WER) - 使用编辑距离"""
        try:
            # 移除多余的空格并规范化
            ref_cleaned = self._clean_text_for_words(reference)
            hyp_cleaned = self._clean_text_for_words(hypothesis)
            
            # 分词
            ref_words = ref_cleaned.split()
            hyp_words = hyp_cleaned.split()
            
            if len(ref_words) == 0:
                return 0.0 if len(hyp_words) == 0 else 1.0
            
            # 使用编辑距离计算 WER
            edit_distance = editdistance.eval(ref_words, hyp_words)
            wer = edit_distance / len(ref_words)
            
            return min(wer, 1.0)  # WER 不应超过 1.0
            
        except Exception as e:
            print(f"Error calculating WER: {e}")
            print(f"Reference: '{reference}'")
            print(f"Hypothesis: '{hypothesis}'")
            # 使用 jiwer 作为备选方案
            try:
                ref_cleaned = self._clean_text_for_words(reference)
                hyp_cleaned = self._clean_text_for_words(hypothesis)
                return jiwer.wer(ref_cleaned, hyp_cleaned)
            except:
                return 1.0
    
    def calculate_accuracy(self, reference: str, hypothesis: str) -> float:
        """计算准确率"""
        try:
            ref_cleaned = self._clean_text(reference)
            hyp_cleaned = self._clean_text(hypothesis)
            
            # 字符级准确率
            ref_chars = list(ref_cleaned)
            hyp_chars = list(hyp_cleaned)
            
            if len(ref_chars) == 0:
                return 1.0 if len(hyp_chars) == 0 else 0.0
            
            # 使用编辑距离计算准确率
            edit_distance = editdistance.eval(ref_chars, hyp_chars)
            accuracy = max(0.0, 1.0 - (edit_distance / max(len(ref_chars), len(hyp_chars))))
            
            return accuracy
            
        except Exception as e:
            print(f"Error calculating accuracy: {e}")
            return 0.0
    
    def _clean_text(self, text: str) -> str:
        """清理和规范化文本（用于字符级计算）"""
        if not text:
            return ""
        
        # 移除所有空格（对于中文字符级计算）
        cleaned = text.replace(' ', '')
        
        return cleaned
    
    def _clean_text_for_words(self, text: str) -> str:
        """清理和规范化文本（用于词级计算）"""
        if not text:
            return ""
        
        # 保留空格，但规范化多个空格为单个空格
        cleaned = ' '.join(text.split())
        
        return cleaned
    
    def calculate_batch_metrics(self, references: List[str], hypotheses: List[str]) -> dict:
        """计算批量指标"""
        if len(references) != len(hypotheses):
            raise ValueError(f"References and hypotheses must have the same length: {len(references)} vs {len(hypotheses)}")
        
        cers = []
        wers = []
        accuracies = []
        successful_samples = 0
        
        for i, (ref, hyp) in enumerate(zip(references, hypotheses)):
            try:
                cer = self.calculate_cer(ref, hyp)
                wer = self.calculate_wer(ref, hyp)
                acc = self.calculate_accuracy(ref, hyp)
                
                cers.append(cer)
                wers.append(wer)
                accuracies.append(acc)
                successful_samples += 1
                
            except Exception as e:
                print(f"Error processing sample {i+1}: {e}")
                print(f"Reference: '{ref}'")
                print(f"Hypothesis: '{hyp}'")
                # 添加默认值以保持列表长度一致
                cers.append(1.0)
                wers.append(1.0)
                accuracies.append(0.0)
        
        return {
            'average_cer': sum(cers) / len(cers) if cers else 1.0,
            'average_wer': sum(wers) / len(wers) if wers else 1.0,
            'average_accuracy': sum(accuracies) / len(accuracies) if accuracies else 0.0,
            'total_samples': len(references),
            'successful_samples': successful_samples,  # 添加这个字段
            'failed_samples': len(references) - successful_samples
        }