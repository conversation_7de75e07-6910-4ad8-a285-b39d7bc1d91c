"""
Speaker Verification配置文件
用于iic/speech_campplus_sv_zh-cn_16k-common模型在cn-celeb1数据集上的验证
"""

# 模型配置
SV_MODEL_ID = "iic/speech_campplus_sv_zh-cn_16k-common"
DEVICE = "cuda:1"  # 或 "cpu"

# CN-Celeb1数据集配置
CNCELEB1_DATA_PATH = "./data/cnceleb1"  # CN-Celeb1数据集根路径
EVAL_TRIALS_PATH = "eval/trials.lst"  # 评估试验列表文件
EVAL_ENROLL_PATH = "eval/enroll.lst"  # 注册音频列表文件
EVAL_TEST_PATH = "eval/test.lst"  # 测试音频列表文件

# 音频配置
SAMPLE_RATE = 16000  # 采样率
MAX_DURATION = 30.0  # 最大音频时长（秒）
MIN_DURATION = 1.0   # 最小音频时长（秒）

# 评估配置
BATCH_SIZE = 32  # 批处理大小
MAX_TRIALS = None  # 最大试验数量，None表示使用全部
EMBEDDING_DIM = 512  # 说话人嵌入维度

# 输出配置
SV_OUTPUT_DIR = "./sv_results"
SV_RESULTS_FILE = "sv_evaluation_results.json"
SV_DETAILED_RESULTS_FILE = "sv_detailed_results.csv"
SV_EMBEDDINGS_FILE = "speaker_embeddings.npz"

# 评估指标配置
# EER计算配置
EER_THRESHOLD_STEP = 0.001  # EER计算的阈值步长

# MinDCF计算配置
DCF_P_TARGET = 0.01  # 目标说话人先验概率
DCF_C_MISS = 1.0     # 漏检代价
DCF_C_FA = 1.0       # 误检代价

# 数据预处理配置
NORMALIZE_AUDIO = True  # 是否标准化音频
REMOVE_SILENCE = False  # 是否移除静音段
