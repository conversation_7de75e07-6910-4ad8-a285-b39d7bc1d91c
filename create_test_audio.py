"""
创建测试音频文件
"""

import os
import numpy as np
import soundfile as sf

def create_test_audio():
    """创建测试音频文件"""
    print("Creating test audio file...")
    
    # 创建目录
    os.makedirs("./data/cnceleb1/CN-Celeb_flac/data/id00001", exist_ok=True)
    
    # 音频参数
    sample_rate = 16000
    duration = 3.0  # 3秒
    samples = int(sample_rate * duration)
    
    # 生成测试音频（正弦波 + 噪声）
    t = np.linspace(0, duration, samples)
    frequency = 440  # A4音符
    audio = 0.5 * np.sin(2 * np.pi * frequency * t)
    
    # 添加一些噪声使其更真实
    noise = 0.1 * np.random.randn(samples)
    audio = audio + noise
    
    # 确保在合理范围内
    audio = np.clip(audio, -1.0, 1.0)
    
    # 保存为FLAC文件
    output_path = "./data/cnceleb1/CN-Celeb_flac/data/id00001/test-01-001.flac"
    sf.write(output_path, audio, sample_rate)
    
    print(f"Test audio saved to: {output_path}")
    print(f"Audio duration: {duration}s, Sample rate: {sample_rate}Hz")
    
    return output_path

if __name__ == "__main__":
    create_test_audio()
