"""
ASR模型评估主脚本
使用iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch模型
在AISHELL-1数据集上进行准确率验证
"""

import os
import json
import pandas as pd
from datetime import datetime
from typing import List, Dict
import torch
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from tqdm import tqdm

from data_loader import AishellDataLoader
from metrics import ASRMetrics
import config
import numpy as np  # 确保导入了 numpy


class ASREvaluator:
    """ASR模型评估器"""
    
    def __init__(self):
        self.device = config.DEVICE if torch.cuda.is_available() else "cpu"
        print(f"Using device: {self.device}")
        
        # 初始化模型
        print("Loading ASR model...")
        self.asr_pipeline = pipeline(
            task=Tasks.auto_speech_recognition,
            model=config.MODEL_ID,
            device=self.device
        )
        print("Model loaded successfully!")
        
        # 初始化数据加载器和指标计算器
        self.data_loader = AishellDataLoader(
            data_path=config.AISHELL_DATA_PATH,
            sample_rate=config.SAMPLE_RATE
        )
        self.metrics = ASRMetrics()
        
        # 创建输出目录
        os.makedirs(config.OUTPUT_DIR, exist_ok=True)
    
    def transcribe_audio(self, audio_data, sample_rate: int) -> str:
        """使用ASR模型转录音频"""
        try:
            # 确保 audio_data 是 numpy 数组
            if not isinstance(audio_data, np.ndarray):
                audio_data = np.array(audio_data, dtype=np.float32)
            
            # 确保是 float32 类型
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)
            
            # 直接传递音频数组给 pipeline
            result = self.asr_pipeline(audio_data)
            
            # 处理不同的返回格式
            if isinstance(result, list) and len(result) > 0:
                # ModelScope 通常返回列表格式
                first_result = result[0]
                if isinstance(first_result, dict) and 'text' in first_result:
                    return first_result['text']
            elif isinstance(result, dict) and 'text' in result:
                # 单个字典格式
                return result['text']
            elif isinstance(result, str):
                # 直接字符串格式
                return result
            else:
                print(f"Warning: Unexpected result format: {result}")
                return ""
                
        except Exception as e:
            print(f"Error during transcription: {e}")
            import traceback
            traceback.print_exc()
            return ""
    
    def evaluate_dataset(self, wav_scp_path: str, text_path: str) -> Dict:
        """评估整个数据集"""
        print("Loading test data...")
        test_data = self.data_loader.get_test_data(
            wav_scp_path=wav_scp_path,
            text_path=text_path,
            # max_samples=100  # 先测试10个样本
        )
        
        if not test_data:
            raise ValueError("No test data loaded!")
        
        print(f"Starting evaluation on {len(test_data)} samples...")
        
        references = []
        hypotheses = []
        detailed_results = []
        
        # 逐个处理音频文件
        for i, sample in enumerate(tqdm(test_data, desc="Transcribing audio")):
            utt_id = sample['utterance_id']
            audio = sample['audio']
            sample_rate = sample['sample_rate']
            reference_text = sample['reference_text']
            
            print(f"\nDEBUG: Processing sample {i+1}: {utt_id}")
            print(f"DEBUG: Reference text: '{reference_text}'")
            
            # 进行语音识别
            hypothesis_text = self.transcribe_audio(audio, sample_rate)
            
            print(f"DEBUG: Hypothesis text: '{hypothesis_text}'")
            
            if not hypothesis_text:  # 如果转录失败，跳过这个样本
                print(f"Warning: Skipping sample {utt_id} due to transcription failure")
                continue
            
            references.append(reference_text)
            hypotheses.append(hypothesis_text)
            
            # 计算单个样本的指标
            print(f"DEBUG: Calculating metrics for sample {utt_id}")
            cer = self.metrics.calculate_cer(reference_text, hypothesis_text)
            wer = self.metrics.calculate_wer(reference_text, hypothesis_text)
            accuracy = self.metrics.calculate_accuracy(reference_text, hypothesis_text)
            
            detailed_results.append({
                'utterance_id': utt_id,
                'audio_path': sample['audio_path'],
                'reference_text': reference_text,
                'hypothesis_text': hypothesis_text,
                'cer': cer,
                'wer': wer,
                'accuracy': accuracy
            })
            
            print(f"Sample {i+1} ({utt_id}):")
            print(f"  Reference: {reference_text}")
            print(f"  Hypothesis: {hypothesis_text}")
            print(f"  CER: {cer:.4f}, WER: {wer:.4f}, Accuracy: {accuracy:.4f}")
            print()
        
        if not references:
            raise ValueError("No samples were successfully transcribed!")
        
        # 计算总体指标
        print("Calculating overall metrics...")
        overall_metrics = self.metrics.calculate_batch_metrics(references, hypotheses)
        
        return {
            'overall_metrics': overall_metrics,
            'detailed_results': detailed_results,
            'evaluation_info': {
                'model_id': config.MODEL_ID,
                'dataset': 'AISHELL-1',
                'total_samples': len(test_data),
                'successful_samples': len(references),
                'evaluation_time': datetime.now().isoformat(),
                'device': self.device
            }
        }
    
    def save_results(self, results: Dict):
        """保存评估结果"""
        # 保存总体结果
        results_file = os.path.join(config.OUTPUT_DIR, config.RESULTS_FILE)
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'overall_metrics': results['overall_metrics'],
                'evaluation_info': results['evaluation_info']
            }, f, ensure_ascii=False, indent=2)
        
        # 保存详细结果
        detailed_file = os.path.join(config.OUTPUT_DIR, config.DETAILED_RESULTS_FILE)
        df = pd.DataFrame(results['detailed_results'])
        df.to_csv(detailed_file, index=False, encoding='utf-8')
        
        print(f"Results saved to {results_file}")
        print(f"Detailed results saved to {detailed_file}")
    
    def print_summary(self, results: Dict):
        """打印评估结果摘要"""
        print("=" * 60)
        print("ASR EVALUATION SUMMARY")
        print("=" * 60)
        
        eval_info = results.get('evaluation_info', {})
        metrics = results.get('overall_metrics', {})
        
        print(f"Model: {eval_info.get('model_id', 'Unknown')}")
        print(f"Dataset: {eval_info.get('dataset', 'Unknown')}")
        print(f"Total samples: {eval_info.get('total_samples', 0)}")
        print(f"Successfully processed: {eval_info.get('successful_samples', 0)}")
        print(f"Device: {eval_info.get('device', 'Unknown')}")
        print(f"Evaluation time: {eval_info.get('evaluation_time', 'Unknown')}")
        print()
        
        print("OVERALL METRICS:")
        print(f"Average CER: {metrics.get('average_cer', 0):.4f}")
        print(f"Average WER: {metrics.get('average_wer', 0):.4f}")
        print(f"Average Accuracy: {metrics.get('average_accuracy', 0):.4f}")
        print(f"Successful samples: {metrics.get('successful_samples', 0)}")
        print(f"Failed samples: {metrics.get('failed_samples', 0)}")
        print()
        
        # 显示一些详细结果示例
        detailed_results = results.get('detailed_results', [])
        if detailed_results:
            print("SAMPLE RESULTS (first 5):")
            for i, result in enumerate(detailed_results[:5]):
                print(f"Sample {i+1} ({result.get('utterance_id', 'Unknown')}):")
                print(f"  Reference: {result.get('reference_text', '')}")
                print(f"  Hypothesis: {result.get('hypothesis_text', '')}")
                print(f"  CER: {result.get('cer', 0):.4f}, WER: {result.get('wer', 0):.4f}, Accuracy: {result.get('accuracy', 0):.4f}")
                print()


def main():
    """主函数"""
    try:
        # 检查数据集路径
        wav_scp_path = os.path.join(config.AISHELL_DATA_PATH, config.TEST_MANIFEST)
        text_path = os.path.join(config.AISHELL_DATA_PATH, config.TEST_TRANSCRIPT)
        
        if not os.path.exists(wav_scp_path):
            print(f"Error: wav.scp file not found at {wav_scp_path}")
            print("Please ensure AISHELL-1 dataset is properly downloaded and configured.")
            return
        
        if not os.path.exists(text_path):
            print(f"Error: text file not found at {text_path}")
            print("Please ensure AISHELL-1 dataset is properly downloaded and configured.")
            return
        
        # 创建评估器并运行评估
        evaluator = ASREvaluator()
        results = evaluator.evaluate_dataset(wav_scp_path, text_path)
        
        # 保存和显示结果
        evaluator.save_results(results)
        evaluator.print_summary(results)
        
    except Exception as e:
        print(f"Evaluation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
